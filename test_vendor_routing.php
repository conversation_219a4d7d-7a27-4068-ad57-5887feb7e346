<?php
/**
 * Test script to debug vendor routing issues
 */

// Initialize the application
require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'core/Controller.php';
require_once 'core/App.php';

echo "<h1>Vendor Routing Debug Test</h1>";

// Test 1: Check if VendorController file exists
echo "<h2>Test 1: VendorController File Check</h2>";
$vendorControllerPath = 'controllers/VendorController.php';
if (file_exists($vendorControllerPath)) {
    echo "✅ VendorController.php exists at: " . realpath($vendorControllerPath) . "<br>";
} else {
    echo "❌ VendorController.php NOT found at: " . $vendorControllerPath . "<br>";
}

// Test 2: Check if VendorModel file exists
echo "<h2>Test 2: VendorModel File Check</h2>";
$vendorModelPath = 'models/VendorModel.php';
if (file_exists($vendorModelPath)) {
    echo "✅ VendorModel.php exists at: " . realpath($vendorModelPath) . "<br>";
} else {
    echo "❌ VendorModel.php NOT found at: " . $vendorModelPath . "<br>";
}

// Test 3: Check if vendor views exist
echo "<h2>Test 3: Vendor Views Check</h2>";
$vendorViewsPath = 'views/vendor';
if (is_dir($vendorViewsPath)) {
    echo "✅ Vendor views directory exists at: " . realpath($vendorViewsPath) . "<br>";
    $viewFiles = scandir($vendorViewsPath);
    foreach ($viewFiles as $file) {
        if ($file !== '.' && $file !== '..') {
            echo "  - " . $file . "<br>";
        }
    }
} else {
    echo "❌ Vendor views directory NOT found at: " . $vendorViewsPath . "<br>";
}

// Test 4: Test URL parsing
echo "<h2>Test 4: URL Parsing Test</h2>";
$testUrls = [
    'vendor',
    'vendor/select/1',
    'vendor/assign',
    'vendor/map/1'
];

foreach ($testUrls as $testUrl) {
    echo "<strong>Testing URL: /{$testUrl}</strong><br>";
    
    // Simulate the URL parsing
    $_GET['url'] = $testUrl;
    $url = explode('/', filter_var(rtrim($_GET['url'], '/'), FILTER_SANITIZE_URL));
    
    echo "  - Parsed URL segments: " . implode(', ', $url) . "<br>";
    
    // Check controller resolution
    if (isset($url[0])) {
        $controllerName = ucfirst($url[0]) . 'Controller';
        $controllerFile = 'controllers/' . $controllerName . '.php';
        
        if (file_exists($controllerFile)) {
            echo "  - ✅ Controller file found: " . $controllerFile . "<br>";
            
            // Check if class exists after including
            require_once $controllerFile;
            if (class_exists($controllerName)) {
                echo "  - ✅ Controller class exists: " . $controllerName . "<br>";
                
                // Check method
                if (isset($url[1])) {
                    $methodName = $url[1];
                    if (method_exists($controllerName, $methodName)) {
                        echo "  - ✅ Method exists: " . $methodName . "<br>";
                    } else {
                        echo "  - ❌ Method NOT found: " . $methodName . "<br>";
                    }
                }
            } else {
                echo "  - ❌ Controller class NOT found: " . $controllerName . "<br>";
            }
        } else {
            echo "  - ❌ Controller file NOT found: " . $controllerFile . "<br>";
        }
    }
    echo "<br>";
}

// Test 5: Database connection test
echo "<h2>Test 5: Database Connection Test</h2>";
try {
    $db = new Database();
    echo "✅ Database connection successful<br>";
    
    // Test if vendor-related tables exist
    $tables = ['event_layouts', 'layout_elements', 'vendor_booth_assignments'];
    foreach ($tables as $table) {
        $db->query("SHOW TABLES LIKE '{$table}'");
        $result = $db->single();
        if ($result) {
            echo "✅ Table exists: {$table}<br>";
        } else {
            echo "❌ Table NOT found: {$table}<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
}

echo "<h2>Test Complete</h2>";
echo "<p>Check the results above to identify the vendor management issue.</p>";
?>
