<?php
require APPROOT . '/views/includes/header.php';

// VERSION CONTROL: Increment this number whenever you make changes to force browser cache refresh
define('LAYOUT_DESIGNER_VERSION', '1.4.4');

// Function to generate consistent colors for categories
function getCategoryColor($categoryName) {
    $colors = [
        '#007bff', '#28a745', '#dc3545', '#ffc107', '#17a2b8',
        '#6f42c1', '#e83e8c', '#fd7e14', '#20c997', '#6c757d'
    ];
    $hash = crc32($categoryName);
    return $colors[abs($hash) % count($colors)];
}
?>

<!-- Force cache refresh when version changes -->
<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="0">
<meta name="version" content="<?php echo LAYOUT_DESIGNER_VERSION; ?>">

<style>
    /* Specific fix for load template modal only - no longer needed since we use DOM portal */
    #loadTemplateModal {
        /* DOM portal handles positioning, no CSS overrides needed */
    }

    /* Compact button styles */
    .btn-xs {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        line-height: 1.2;
        border-radius: 0.2rem;
    }

    /* Reduced padding for action buttons */
    .btn-compact {
        padding: 0.375rem 0.25rem !important;
    }

    /* Draggable category badges */
    .category-badge {
        transition: transform 0.2s ease;
        user-select: none;
    }

    .category-badge:hover {
        transform: scale(1.05);
    }

    .category-badge:active {
        cursor: grabbing !important;
        transform: scale(0.95);
    }

    /* Canvas drop zone styling */
    .design-canvas.drag-over {
        background-color: rgba(0, 123, 255, 0.1);
        border: 2px dashed #007bff;
    }

    /* Drawing tools styling */
    .color-btn {
        border: 2px solid transparent;
        border-radius: 4px;
    }

    .color-btn.active {
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }

    .drawing-tool-active {
        background-color: #007bff !important;
        color: white !important;
        border-color: #007bff !important;
    }

    /* Compact Drawing Toolbar Styling */
    .compact-drawing-toolbar {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 8px 12px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .compact-drawing-toolbar .toolbar-section {
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .compact-drawing-toolbar .toolbar-divider {
        width: 1px;
        height: 24px;
        background-color: #dee2e6;
        margin: 0 8px;
    }

    .compact-drawing-toolbar .tool-btn {
        width: 32px;
        height: 32px;
        padding: 0;
        border: 1px solid #ced4da;
        border-radius: 4px;
        background: white;
        color: #495057;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.15s ease;
        position: relative;
    }

    .compact-drawing-toolbar .tool-btn:hover {
        background: #e9ecef;
        border-color: #adb5bd;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.15);
    }

    .compact-drawing-toolbar .tool-btn.active {
        background: #007bff;
        border-color: #007bff;
        color: white;
        box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
    }

    .compact-drawing-toolbar .tool-btn i {
        font-size: 14px;
    }

    .compact-drawing-toolbar .color-picker {
        width: 32px;
        height: 32px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        cursor: pointer;
        padding: 0;
        background: none;
    }

    .compact-drawing-toolbar .width-slider {
        width: 80px;
        height: 4px;
        margin: 0 8px;
    }

    .compact-drawing-toolbar .toolbar-label {
        font-size: 11px;
        color: #6c757d;
        font-weight: 500;
        margin-right: 4px;
        white-space: nowrap;
    }

    .compact-drawing-toolbar .width-display {
        font-size: 11px;
        color: #495057;
        min-width: 24px;
        text-align: center;
    }

    /* Tooltip for compact toolbar */
    .compact-drawing-toolbar .tool-btn[title]:hover::after {
        content: attr(title);
        position: absolute;
        bottom: -28px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 11px;
        white-space: nowrap;
        z-index: 1000;
        pointer-events: none;
    }

    /* Mobile responsive adjustments */
    @media (max-width: 768px) {
        .compact-drawing-toolbar {
            padding: 6px 8px;
        }

        .compact-drawing-toolbar .d-flex {
            flex-wrap: wrap;
            gap: 8px;
        }

        .compact-drawing-toolbar .toolbar-section {
            gap: 4px;
        }

        .compact-drawing-toolbar .toolbar-divider {
            display: none;
        }

        .compact-drawing-toolbar .toolbar-label {
            font-size: 10px;
            margin-right: 2px;
        }

        .compact-drawing-toolbar .width-slider {
            width: 60px;
        }
    }

    @media (max-width: 576px) {
        .compact-drawing-toolbar .toolbar-section {
            flex-wrap: wrap;
        }

        .compact-drawing-toolbar .width-slider {
            width: 50px;
        }
    }

    /* Drawing Tips Styling */
    .drawing-tips {
        background: rgba(0, 123, 255, 0.1);
        border: 1px solid rgba(0, 123, 255, 0.2);
        border-radius: 4px;
        padding: 4px 8px;
    }

    .drawing-tips kbd {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 3px;
        padding: 2px 4px;
        font-size: 10px;
        font-weight: bold;
        color: #495057;
    }

    #drawing-canvas {
        transition: pointer-events 0.2s ease;
    }

    /* Horizontal stats and categories styling */
    .card-body.py-2 {
        padding-top: 0.5rem !important;
        padding-bottom: 0.5rem !important;
    }

    /* Element table styling */
    #elements-table tbody tr {
        cursor: pointer;
    }

    #elements-table tbody tr.selected {
        background-color: #fff3cd !important;
    }

    #elements-table tbody tr:hover {
        background-color: #f8f9fa;
    }

    #elements-table tbody tr.selected:hover {
        background-color: #ffeaa7 !important;
    }

    .element-color-indicator {
        width: 20px;
        height: 20px;
        border-radius: 4px;
        border: 2px solid #dee2e6;
        display: inline-block;
        box-shadow: inset 0 0 0 1px rgba(0,0,0,0.1);
    }

    /* Resize handles */
    .resize-handle {
        position: absolute;
        background: #007bff;
        border: 2px solid white;
        border-radius: 50%;
        width: 12px;
        height: 12px;
        cursor: pointer;
        z-index: 1000;
    }

    .resize-handle.nw { top: -6px; left: -6px; cursor: nw-resize; }
    .resize-handle.ne { top: -6px; right: -6px; cursor: ne-resize; }
    .resize-handle.sw { bottom: -6px; left: -6px; cursor: sw-resize; }
    .resize-handle.se { bottom: -6px; right: -6px; cursor: se-resize; }
    .resize-handle.n { top: -6px; left: 50%; transform: translateX(-50%); cursor: n-resize; }
    .resize-handle.s { bottom: -6px; left: 50%; transform: translateX(-50%); cursor: s-resize; }
    .resize-handle.w { top: 50%; left: -6px; transform: translateY(-50%); cursor: w-resize; }
    .resize-handle.e { top: 50%; right: -6px; transform: translateY(-50%); cursor: e-resize; }

    /* Rotation handle */
    .rotation-handle {
        position: absolute;
        background: #28a745;
        border: 2px solid white;
        border-radius: 50%;
        width: 14px;
        height: 14px;
        cursor: grab;
        z-index: 1001;
        top: -25px;
        left: 50%;
        transform: translateX(-50%);
    }

    .rotation-handle:active {
        cursor: grabbing;
    }

    /* Force rotation visibility */
    .rotating {
        transform-origin: center center !important;
        will-change: transform !important;
        transition: none !important; /* eliminate easing during rotation for snappier feel */
    }

    .dragging {
        transition: none !important; /* eliminate transitions during dragging for immediate response */
    }

    /* Context menu styles for overlapping elements */
    .context-menu {
        position: absolute;
        background: white;
        border: 1px solid #ccc;
        border-radius: 4px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        z-index: 10000;
        min-width: 200px;
        max-height: 300px;
        overflow-y: auto;
    }

    .context-menu-item {
        padding: 8px 12px;
        cursor: pointer;
        border-bottom: 1px solid #eee;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .context-menu-item:hover {
        background-color: #f0f0f0;
    }

    .context-menu-item:last-child {
        border-bottom: none;
    }

    .element-type-indicator {
        width: 16px;
        height: 16px;
        border-radius: 2px;
        flex-shrink: 0;
    }

    /* Toolbar action buttons container */
    .toolbar-actions {
        display: flex;
        gap: 8px;
        align-items: center;
    }

    /* Layout Designer Dropdown menu styles - scoped to avoid affecting header */
    .toolbar-actions .dropdown-container {
        position: relative;
        display: inline-block;
    }

    .toolbar-actions .dropdown-btn {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: 1px solid #0056b3;
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.2s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        gap: 6px;
        min-width: 100px;
        justify-content: space-between;
    }

    .toolbar-actions .dropdown-btn:hover {
        background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .toolbar-actions .dropdown-btn.disabled {
        background: #e9ecef;
        color: #6c757d;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .toolbar-actions .dropdown-arrow {
        font-size: 10px;
        transition: transform 0.2s ease;
    }

    .toolbar-actions .dropdown-container.open .dropdown-arrow {
        transform: rotate(180deg);
    }

    .toolbar-actions .dropdown-menu {
        position: absolute;
        top: 100%;
        left: 0;
        background: white;
        border: 1px solid #ccc;
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        min-width: 180px;
        max-height: 300px;
        overflow-y: auto;
        display: none;
    }

    .toolbar-actions .dropdown-container.open .dropdown-menu {
        display: block;
    }

    .toolbar-actions .dropdown-item {
        padding: 10px 15px;
        cursor: pointer;
        border-bottom: 1px solid #eee;
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 14px;
        transition: background-color 0.2s ease;
    }

    .toolbar-actions .dropdown-item:hover {
        background-color: #f8f9fa;
    }

    .toolbar-actions .dropdown-item:last-child {
        border-bottom: none;
    }

    .toolbar-actions .dropdown-item.disabled {
        color: #6c757d;
        cursor: not-allowed;
        background-color: #f8f9fa;
    }

    .toolbar-actions .dropdown-item i {
        width: 16px;
        text-align: center;
        color: #007bff;
    }

    .toolbar-actions .dropdown-header {
        padding: 8px 15px;
        font-weight: bold;
        font-size: 12px;
        color: #6c757d;
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .design-canvas {
        background: transparent;
        position: relative;
        cursor: crosshair;
        transform-origin: top left;
    }



    .canvas-container {
        background: #f8f9fa;
        border: 2px dashed #dee2e6;
        /* Container grid that aligns with canvas grid */
        background-image:
            linear-gradient(rgba(0,0,0,.05) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0,0,0,.05) 1px, transparent 1px);
        background-size: 10px 10px;
    }

    /* Equal height system */
    .equal-height-row {
        display: flex;
        flex-wrap: wrap;
    }

    .equal-height-col {
        display: flex;
        flex-direction: column;
    }

    .equal-height-col .card {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .equal-height-col .card-body {
        flex: 1;
    }



    .layout-element {
        position: absolute;
        border: 2px solid;
        border-radius: 8px;
        cursor: move;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        user-select: none;
        transition: all 0.2s ease;
        z-index: 10;
    }

    .layout-element:hover {
        transform: scale(1.02);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .layout-element.selected {
        border-color: #ffc107 !important;
        box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.25);
    }

    .show-parking {
        background: rgba(0, 123, 255, 0.1);
        border-color: #007bff;
        color: #007bff;
    }

    .visitor-parking {
        background: rgba(23, 162, 184, 0.1);
        border-color: #17a2b8;
        color: #17a2b8;
    }

    .visitor-parking.handicap {
        background: rgba(40, 167, 69, 0.2);
        border-color: #28a745;
        color: #28a745;
    }

    .building {
        background: rgba(108, 117, 125, 0.15);
        border-color: #6c757d;
        color: #6c757d;
    }

    .vendor-area {
        background: rgba(255, 193, 7, 0.1);
        border-color: #ffc107;
        color: #856404;
    }

    .vendor-booth {
        background: rgba(255, 193, 7, 0.2);
        border-color: #ffc107;
        color: #856404;
    }

    .facility {
        background: rgba(108, 117, 125, 0.1);
        border-color: #6c757d;
        color: #6c757d;
    }

    .special-area {
        background: rgba(40, 167, 69, 0.1);
        border-color: #28a745;
        color: #28a745;
    }

    /* Context menu styles temporarily removed */


    .element-properties {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 2000;
        background: rgba(255,255,255,0.98);
        border-radius: 8px;
        padding: 0;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        min-width: 300px;
        max-width: 90vw;
        max-height: 80vh;
        border: 1px solid #dee2e6;
        overflow: hidden;
    }

    .element-properties-header {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        padding: 10px 15px;
        cursor: move;
        user-select: none;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 500;
        color: #495057;
    }

    .element-properties-header:hover {
        background: #e9ecef;
    }

    .element-properties-content {
        padding: 15px;
        max-height: calc(80vh - 50px);
        overflow-y: auto;
    }

    .element-properties-close {
        background: none;
        border: none;
        font-size: 18px;
        color: #6c757d;
        cursor: pointer;
        padding: 0;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .element-properties-close:hover {
        color: #dc3545;
    }

    /* Group Manager Panel - Photoshop Style */
    .group-manager {
        position: fixed;
        top: 20px;
        right: 20px;
        width: 280px;
        max-height: 70vh;
        background: #2c2c2c;
        border: 1px solid #555;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 1000;
        overflow: hidden;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .group-manager-header {
        background: #383838;
        color: #e0e0e0;
        padding: 8px 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #555;
    }

    .group-manager-header h6 {
        margin: 0;
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .group-manager-header button {
        background: transparent;
        border: none;
        color: #e0e0e0;
        padding: 2px 6px;
        border-radius: 2px;
        font-size: 11px;
        opacity: 0.7;
    }

    .group-manager-header button:hover {
        background: rgba(255,255,255,0.1);
        opacity: 1;
    }

    #group-manager-content {
        padding: 8px;
        max-height: calc(70vh - 40px);
        overflow-y: auto;
        background: #2c2c2c;
    }

    /* Photoshop-style Group Items */
    .group-item {
        background: #3a3a3a;
        border: none;
        border-bottom: 1px solid #555;
        padding: 0;
        margin: 0;
        transition: background-color 0.15s ease;
    }

    .group-item:hover {
        background: #404040;
    }

    .group-item.selected {
        background: #4a90e2;
    }

    .group-header {
        display: flex;
        align-items: center;
        padding: 6px 8px;
        cursor: pointer;
        user-select: none;
    }

    .group-toggle {
        width: 12px;
        height: 12px;
        margin-right: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 8px;
        color: #999;
        cursor: pointer;
    }

    .group-icon {
        width: 16px;
        height: 16px;
        margin-right: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        color: #ccc;
    }

    .group-name {
        font-size: 11px;
        color: #e0e0e0;
        flex: 1;
        font-weight: 400;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .group-actions {
        display: flex;
        gap: 2px;
        opacity: 0;
        transition: opacity 0.15s ease;
    }

    .group-item:hover .group-actions {
        opacity: 1;
    }

    .group-actions button {
        background: transparent;
        border: none;
        color: #ccc;
        padding: 2px 4px;
        border-radius: 2px;
        font-size: 10px;
        cursor: pointer;
        opacity: 0.7;
    }

    .group-actions button:hover {
        background: rgba(255,255,255,0.1);
        opacity: 1;
    }

    .group-elements {
        padding: 4px 8px 6px 30px;
        font-size: 10px;
        color: #999;
        background: #333;
        border-top: 1px solid #444;
        display: none;
    }

    .group-item.expanded .group-elements {
        display: block;
    }

    .group-element-item {
        display: flex;
        align-items: center;
        padding: 2px 0;
        color: #bbb;
        cursor: pointer;
        border-radius: 2px;
        transition: background-color 0.15s ease;
    }

    .group-element-item:hover {
        background-color: rgba(255,255,255,0.1);
        color: #fff;
    }

    .group-element-item.selected {
        background-color: #0078d4;
        color: #fff;
    }

    .group-element-icon {
        width: 12px;
        height: 12px;
        margin-right: 6px;
        font-size: 8px;
        color: #888;
    }

    .group-element-item:hover .group-element-icon,
    .group-element-item.selected .group-element-icon {
        color: #fff;
    }

    .group-element-name {
        font-size: 10px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Create Group Button */
    .create-group-btn {
        background: #4a90e2 !important;
        border: none !important;
        color: white !important;
        padding: 6px 12px !important;
        font-size: 11px !important;
        border-radius: 3px !important;
        margin-bottom: 8px !important;
    }

    .create-group-btn:hover {
        background: #357abd !important;
    }

    /* Multi-selection styles */
    .layout-element.multi-selected {
        border: 3px solid #28a745 !important;
        box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.3) !important;
    }

    .layout-element.selected {
        border: 3px solid #007bff !important;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.3) !important;
    }

    /* Selection counter */
    .selection-counter {
        position: fixed;
        top: 10px;
        left: 50%;
        transform: translateX(-50%);
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 600;
        z-index: 1500;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        display: none;
        animation: slideDown 0.3s ease;
    }

    @keyframes slideDown {
        from { transform: translateX(-50%) translateY(-20px); opacity: 0; }
        to { transform: translateX(-50%) translateY(0); opacity: 1; }
    }

    .selection-counter.show {
        display: block;
    }

    /* Group indicator styles */
    .layout-element.grouped {
        border-left: 4px solid #ffc107 !important;
        position: relative;
    }

    .layout-element.grouped::before {
        content: "📦";
        position: absolute;
        top: -2px;
        right: -2px;
        background: #ffc107;
        color: white;
        font-size: 10px;
        padding: 1px 3px;
        border-radius: 3px;
        z-index: 10;
    }

    .element-type-btn {
        margin: 2px;
        font-size: 0.875rem;
    }

    .stats-panel {
        background: rgba(255,255,255,0.95);
        border-radius: 8px;
        padding: 15px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }


</style>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="mb-4">
                <div class="mb-3">
                    <h2><i class="fas fa-pencil-ruler"></i> Layout Designer</h2>
                    <p class="text-muted mb-0">
                        <strong><?= htmlspecialchars($show->name) ?></strong> -
                        <?= date('M j, Y', strtotime($show->start_date)) ?>
                        <?php if ($show->venue_name): ?>
                            at <?= htmlspecialchars($show->venue_name) ?>
                        <?php endif; ?>
                    </p>
                </div>
                <!-- Stats and Buttons Row -->
                <div class="d-flex align-items-center gap-3 mb-3">
                    <!-- Stats Section (Compact) -->
                    <div class="flex-shrink-0" style="width: 31%;">
                        <div class="card">
                            <div class="card-body py-2">
                                <div class="d-flex align-items-center">
                                    <h6 class="mb-0 me-2"><i class="fas fa-chart-bar"></i> Stats</h6>
                                    <div class="d-flex gap-2">
                                        <div class="text-center">
                                            <div class="text-primary fw-bold" id="stats-show-parking">0</div>
                                            <small class="text-muted">Show Parking</small>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-warning fw-bold" id="stats-visitor-parking">0</div>
                                            <small class="text-muted">Visitor Parking</small>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-success fw-bold" id="stats-vendor-booths">0</div>
                                            <small class="text-muted">Vendor Booths</small>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-info fw-bold" id="stats-total-capacity">0</div>
                                            <small class="text-muted">Total Parking</small>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-secondary fw-bold" id="zoom-level">100%</div>
                                            <small class="text-muted">Zoom</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-center flex-wrap gap-2" style="width: 69%;">
                        <button class="btn btn-outline-secondary btn-compact" style="width: 160px;" onclick="loadTemplate()">
                            <i class="fas fa-layer-group"></i> Load Template
                        </button>
                        <button class="btn btn-outline-info btn-compact" style="width: 160px;" onclick="saveAsTemplate()">
                            <i class="fas fa-save"></i> Save Template
                        </button>
                        <button class="btn btn-success btn-compact" style="width: 160px;" onclick="saveLayout()">
                            <i class="fas fa-save"></i> Save Layout
                        </button>
                        <a href="<?= URLROOT ?>/layout_manager/sections/<?= $show->id ?>" class="btn btn-primary btn-compact" style="width: 160px;">
                            <i class="fas fa-th-large"></i> Manage Sections
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Categories Bar (Full Width) -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body py-2">
                    <div class="d-flex align-items-center">
                        <h6 class="mb-0 me-3"><i class="fas fa-tags"></i> Categories</h6>
                        <div class="d-flex flex-wrap gap-2">
                            <?php if (empty($categories)): ?>
                                <span class="text-muted small">No categories defined</span>
                                <a href="<?= URLROOT ?>/shows/edit/<?= $show->id ?>" class="btn btn-xs btn-outline-primary ms-2">
                                    <i class="fas fa-plus"></i> Add
                                </a>
                            <?php else: ?>
                                <?php foreach ($categories as $category): ?>
                                    <span class="badge category-badge"
                                          draggable="true"
                                          data-category-id="<?= $category->id ?>"
                                          data-category-name="<?= htmlspecialchars($category->name) ?>"
                                          style="background-color: <?= getCategoryColor($category->name) ?>; cursor: grab;">
                                        <i class="fas fa-grip-vertical me-1"></i>
                                        <?= htmlspecialchars($category->name) ?> (<?= $category->vehicle_count ?? 0 ?>)
                                    </span>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Help Text -->
                    <div class="mt-2 pt-2 border-top">
                        <div class="text-muted small d-flex flex-wrap gap-3" style="font-size: 0.75rem; line-height: 1.3;">
                            <span><i class="fas fa-mouse-pointer me-1"></i> Click to select</span>
                            <span><i class="fas fa-mouse me-1"></i> Double-click for properties</span>
                            <span><i class="fas fa-arrows-alt me-1"></i> Drag to move</span>
                            <span><i class="fas fa-expand-arrows-alt me-1"></i> Drag corners/edges to resize</span>
                            <span><i class="fas fa-grip-vertical me-1"></i> Drag categories to grid to auto-create parking</span>
                            <span><i class="fas fa-layer-group me-1"></i> Right-click overlapping elements to select</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Compact Drawing Toolbar -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="compact-drawing-toolbar">
                <div class="d-flex align-items-center flex-wrap gap-2">
                    <!-- Drawing Tools Section -->
                    <div class="toolbar-section">
                        <span class="toolbar-label">DRAW:</span>
                        <button class="tool-btn" id="tool-freehand" onclick="selectDrawingTool('freehand')" title="Freehand Drawing">
                            <i class="fas fa-pencil-alt"></i>
                        </button>
                        <button class="tool-btn" id="tool-line" onclick="selectDrawingTool('line')" title="Straight Line">
                            <i class="fas fa-minus"></i>
                        </button>
                        <button class="tool-btn" id="tool-rectangle" onclick="selectDrawingTool('rectangle')" title="Rectangle">
                            <i class="fas fa-square"></i>
                        </button>
                        <button class="tool-btn" id="tool-circle" onclick="selectDrawingTool('circle')" title="Circle">
                            <i class="fas fa-circle"></i>
                        </button>
                        <button class="tool-btn" id="tool-road" onclick="selectDrawingTool('road')" title="Road • Hold Shift while drawing for angle snapping (0°, 45°, 90°, etc.)">
                            <i class="fas fa-road"></i>
                        </button>
                        <button class="tool-btn" id="tool-island" onclick="selectDrawingTool('island')" title="Island/Landscaping">
                            <i class="fas fa-tree"></i>
                        </button>
                        <button class="tool-btn" id="tool-text" onclick="selectDrawingTool('text')" title="Text Label">
                            <i class="fas fa-font"></i>
                        </button>
                    </div>

                    <div class="toolbar-divider"></div>

                    <!-- Color Section -->
                    <div class="toolbar-section">
                        <span class="toolbar-label">COLOR:</span>
                        <input type="color" class="color-picker" id="drawing-color" value="#666666"
                               onchange="updateDrawingColor(this.value)" title="Drawing Color">
                    </div>

                    <div class="toolbar-divider"></div>

                    <!-- Width/Size Section -->
                    <div class="toolbar-section">
                        <span class="toolbar-label">WIDTH:</span>
                        <input type="range" class="width-slider" id="drawing-width" min="1" max="50" value="10"
                               oninput="updateDrawingWidth(this.value)" title="Line Width / Shape Size">
                        <span class="width-display" id="width-display">10px</span>
                    </div>

                    <div class="toolbar-divider"></div>

                    <!-- Fill Section -->
                    <div class="toolbar-section">
                        <span class="toolbar-label">FILL:</span>
                        <button class="tool-btn" id="fill-toggle" onclick="toggleDrawingFill()" title="Toggle Fill">
                            <i class="fas fa-fill-drip"></i>
                        </button>
                    </div>

                    <div class="toolbar-divider"></div>

                    <!-- Quick Colors -->
                    <div class="toolbar-section">
                        <span class="toolbar-label">QUICK:</span>
                        <button class="tool-btn" onclick="setQuickColor('#000000')" title="Black" style="background: #000000; border-color: #000000;"></button>
                        <button class="tool-btn" onclick="setQuickColor('#dc3545')" title="Red" style="background: #dc3545; border-color: #dc3545;"></button>
                        <button class="tool-btn" onclick="setQuickColor('#28a745')" title="Green" style="background: #28a745; border-color: #28a745;"></button>
                        <button class="tool-btn" onclick="setQuickColor('#007bff')" title="Blue" style="background: #007bff; border-color: #007bff;"></button>
                        <button class="tool-btn" onclick="setQuickColor('#ffc107')" title="Yellow" style="background: #ffc107; border-color: #ffc107;"></button>
                    </div>

                    <div class="toolbar-divider"></div>

                    <!-- Actions -->
                    <div class="toolbar-section">
                        <button class="tool-btn" onclick="clearAllDrawings()" title="Clear All Drawings" style="color: #dc3545;">
                            <i class="fas fa-eraser"></i>
                        </button>
                    </div>
                </div>

                <!-- Drawing Tips -->
                <div class="drawing-tips" style="margin-top: 8px; font-size: 11px; color: #666; text-align: center;">
                    <i class="fas fa-info-circle"></i>
                    <strong>Tip:</strong> Hold <kbd>Shift</kbd> while drawing or resizing circles to maintain perfect circles
                    <span style="margin-left: 15px;">•</span>
                    <strong>Tip:</strong> Hold <kbd>Shift</kbd> while drawing or rotating roads for angle snapping
                </div>
            </div>
        </div>
    </div>

    <div class="row equal-height-row">
        <!-- Add Elements Toolbar (Left) -->
        <div class="col-lg-3 equal-height-col">
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-tools"></i> Add Elements</h6>
                </div>
                <div class="card-body">
                    <!-- Parking Areas -->
                    <div class="mb-3">
                        <label class="form-label small fw-bold text-primary">Parking Areas</label>
                        <button class="btn btn-sm btn-outline-primary w-100 mb-1" onclick="addElement('show_parking')">
                            <i class="fas fa-car"></i> Show Parking
                        </button>
                        <button class="btn btn-sm btn-outline-info w-100 mb-1" onclick="addElement('visitor_parking')">
                            <i class="fas fa-parking"></i> Visitor Parking
                        </button>
                    </div>

                    <!-- Vendor Areas -->
                    <div class="mb-3">
                        <label class="form-label small fw-bold text-warning">Vendor Areas</label>
                        <button class="btn btn-sm btn-outline-warning w-100 mb-1" onclick="addElement('vendor_area')">
                            <i class="fas fa-store"></i> Vendor Area
                        </button>
                        <button class="btn btn-sm btn-outline-warning w-100 mb-1" onclick="addElement('vendor_booth')">
                            <i class="fas fa-store-alt"></i> Single Booth
                        </button>
                    </div>

                    <!-- Facilities -->
                    <div class="mb-3">
                        <label class="form-label small fw-bold text-secondary">Facilities</label>
                        <button class="btn btn-sm btn-outline-secondary w-100 mb-1" onclick="addElement('building')">
                            <i class="fas fa-building"></i> Building
                        </button>
                        <button class="btn btn-sm btn-outline-secondary w-100 mb-1" onclick="addElement('entrance')">
                            <i class="fas fa-door-open"></i> Entrance
                        </button>
                        <button class="btn btn-sm btn-outline-secondary w-100 mb-1" onclick="addElement('exit')">
                            <i class="fas fa-door-closed"></i> Exit
                        </button>
                        <button class="btn btn-sm btn-outline-secondary w-100 mb-1" onclick="addElement('restroom')">
                            <i class="fas fa-restroom"></i> Restroom
                        </button>
                        <button class="btn btn-sm btn-outline-secondary w-100 mb-1" onclick="addElement('food_area')">
                            <i class="fas fa-utensils"></i> Food Area
                        </button>
                    </div>

                    <!-- Special Areas -->
                    <div class="mb-3">
                        <label class="form-label small fw-bold text-success">Special Areas</label>
                        <button class="btn btn-sm btn-outline-success w-100 mb-1" onclick="addElement('stage')">
                            <i class="fas fa-music"></i> Stage
                        </button>
                        <button class="btn btn-sm btn-outline-success w-100 mb-1" onclick="addElement('judging_area')">
                            <i class="fas fa-trophy"></i> Judging Area
                        </button>
                        <button class="btn btn-sm btn-outline-success w-100 mb-1" onclick="addElement('display_area')">
                            <i class="fas fa-eye"></i> Display Area
                        </button>
                        <button class="btn btn-sm btn-outline-success w-100 mb-1" onclick="addElement('kids_area')">
                            <i class="fas fa-child"></i> Kids Area
                        </button>
                        <button class="btn btn-sm btn-outline-info w-100 mb-1" onclick="addElement('registration_area')">
                            <i class="fas fa-clipboard-check"></i> Registration/Check-in
                        </button>
                        <button class="btn btn-sm btn-outline-danger w-100 mb-1" onclick="addElement('emergency')">
                            <i class="fas fa-ambulance"></i> Emergency
                        </button>
                    </div>
                </div>
            </div>


        </div>

        <!-- Design Canvas (Right) -->
        <div class="col-lg-9 equal-height-col">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0"><i class="fas fa-pencil-ruler"></i> Design Canvas</h6>
                    <div class="toolbar-actions">
                        <!-- Alignment Dropdown -->
                        <div class="dropdown-container" id="align-dropdown">
                            <button class="dropdown-btn" onclick="toggleDropdown('align-dropdown')" title="Align Elements">
                                <i class="fas fa-align-center"></i>
                                <span>Align</span>
                                <i class="fas fa-chevron-down dropdown-arrow"></i>
                            </button>
                            <div class="dropdown-menu">
                                <div class="dropdown-header">Horizontal Alignment</div>
                                <div class="dropdown-item" onclick="alignElements('left')">
                                    <i class="fas fa-align-left"></i>
                                    <span>Align Left</span>
                                </div>
                                <div class="dropdown-item" onclick="alignElements('center-h')">
                                    <i class="fas fa-align-center"></i>
                                    <span>Align Center</span>
                                </div>
                                <div class="dropdown-item" onclick="alignElements('right')">
                                    <i class="fas fa-align-right"></i>
                                    <span>Align Right</span>
                                </div>
                                <div class="dropdown-header">Vertical Alignment</div>
                                <div class="dropdown-item" onclick="alignElements('top')">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>Align Top</span>
                                </div>
                                <div class="dropdown-item" onclick="alignElements('center-v')">
                                    <i class="fas fa-arrows-alt-v"></i>
                                    <span>Align Middle</span>
                                </div>
                                <div class="dropdown-item" onclick="alignElements('bottom')">
                                    <i class="fas fa-arrow-down"></i>
                                    <span>Align Bottom</span>
                                </div>
                                <div class="dropdown-header">Distribution</div>
                                <div class="dropdown-item" onclick="distributeElements('horizontal')">
                                    <i class="fas fa-arrows-alt-h"></i>
                                    <span>Distribute Horizontally</span>
                                </div>
                                <div class="dropdown-item" onclick="distributeElements('vertical')">
                                    <i class="fas fa-arrows-alt-v"></i>
                                    <span>Distribute Vertically</span>
                                </div>
                                <div class="dropdown-item" onclick="distributeElements('horizontal-no-spacing')">
                                    <i class="fas fa-grip-lines-vertical"></i>
                                    <span>Distribute Horizontal (No Spacing)</span>
                                </div>
                                <div class="dropdown-item" onclick="distributeElements('vertical-no-spacing')">
                                    <i class="fas fa-grip-lines"></i>
                                    <span>Distribute Vertical (No Spacing)</span>
                                </div>
                            </div>
                        </div>

                        <!-- Grouping Dropdown -->
                        <div class="dropdown-container" id="group-dropdown">
                            <button class="dropdown-btn" onclick="toggleDropdown('group-dropdown')" title="Group Elements">
                                <i class="fas fa-object-group"></i>
                                <span>Group</span>
                                <i class="fas fa-chevron-down dropdown-arrow"></i>
                            </button>
                            <div class="dropdown-menu">
                                <div class="dropdown-item" onclick="createGroup()">
                                    <i class="fas fa-object-group"></i>
                                    <span>Create Group</span>
                                </div>
                                <div class="dropdown-item" onclick="ungroupElements()">
                                    <i class="fas fa-object-ungroup"></i>
                                    <span>Ungroup</span>
                                </div>
                                <div class="dropdown-item" onclick="toggleGroupManager()">
                                    <i class="fas fa-list"></i>
                                    <span>Manage Groups</span>
                                </div>
                            </div>
                        </div>

                        <div class="btn-group me-2">
                            <button id="undo-btn" class="btn btn-sm btn-outline-primary" onclick="undo()" title="Undo (Ctrl+Z)" disabled>
                                <i class="fas fa-undo"></i>
                            </button>
                            <button id="redo-btn" class="btn btn-sm btn-outline-primary" onclick="redo()" title="Redo (Ctrl+Y)" disabled>
                                <i class="fas fa-redo"></i>
                            </button>
                        </div>
                        <div class="btn-group me-2">
                            <button class="btn btn-sm btn-outline-secondary" onclick="zoomIn()" title="Zoom In">
                                <i class="fas fa-search-plus"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="zoomOut()" title="Zoom Out">
                                <i class="fas fa-search-minus"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="resetZoom()" title="Reset Zoom">
                                <i class="fas fa-expand-arrows-alt"></i>
                            </button>
                        </div>
                        <button class="btn btn-sm btn-outline-danger" onclick="clearCanvas()" title="Clear All">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="canvas-container" style="width: 100%; height: 700px; overflow: auto; position: relative; background: #f8f9fa; background-image: linear-gradient(to right, #dee2e6 1px, transparent 1px), linear-gradient(to bottom, #dee2e6 1px, transparent 1px); background-size: 10px 10px;">
                        <!-- Drawing Canvas Layer (Behind Elements but Above Grid) -->
                        <canvas id="drawing-canvas"
                                width="2000"
                                height="1400"
                                style="position: absolute; top: 0; left: 0; z-index: 2; pointer-events: none;">
                        </canvas>

                        <div id="design-canvas" class="design-canvas" style="width: 2000px; height: 1400px; position: relative; z-index: 3; background: transparent;">
                            <!-- Layout elements will be rendered here by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Element Properties Panel (Fixed to viewport) -->
    <div id="element-properties" class="element-properties" style="display: none;">
        <div class="element-properties-header" id="properties-header">
            <span><i class="fas fa-cog"></i> Element Properties</span>
            <button class="element-properties-close" onclick="closeElementProperties()" title="Close">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="element-properties-content">
            <div id="properties-content">
                <!-- Properties will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Group Management Panel (Fixed to viewport) -->
    <div id="group-manager" class="group-manager" style="display: none;">
        <div class="group-manager-header">
            <h6><i class="fas fa-object-group"></i> Group Manager</h6>
            <button class="btn btn-sm btn-outline-secondary" onclick="toggleGroupManager()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div id="group-manager-content">
            <button class="create-group-btn w-100" onclick="createGroup()">
                <i class="fas fa-plus"></i> Create Group
            </button>
            <div id="groups-list">
                <!-- Groups will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Selection Counter -->
    <div id="selection-counter" class="selection-counter">
        <i class="fas fa-mouse-pointer"></i>
        <span id="selection-count">0</span> elements selected
        <small style="margin-left: 10px; opacity: 0.8;">Hold Ctrl to select multiple</small>
    </div>

    <!-- Layout Elements List (Compact Table) -->
    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-list"></i> Layout Elements
                            <span id="element-count" class="badge bg-secondary ms-2">0</span>
                        </h6>
                        <div class="d-flex gap-2">
                            <select id="type-filter" class="form-select form-select-sm" style="width: auto;" onchange="filterByType()">
                                <option value="">All Types</option>
                                <optgroup label="Grid Elements">
                                    <option value="show_parking">Show Parking</option>
                                    <option value="visitor_parking">Visitor Parking</option>
                                    <option value="vendor_area">Vendor Areas</option>
                                    <option value="vendor_booth">Vendor Booths</option>
                                    <option value="building">Buildings</option>
                                    <option value="entrance">Entrances</option>
                                    <option value="exit">Exits</option>
                                    <option value="restroom">Restrooms</option>
                                    <option value="food_area">Food Areas</option>
                                    <option value="stage">Stages</option>
                                    <option value="judging_area">Judging Areas</option>
                                    <option value="display_area">Display Areas</option>
                                    <option value="kids_area">Kids Areas</option>
                                    <option value="registration_area">Registration</option>
                                    <option value="emergency">Emergency</option>
                                </optgroup>
                                <optgroup label="Canvas Elements">
                                    <option value="road">Roads</option>
                                    <option value="rectangle">Rectangles</option>
                                    <option value="circle">Circles</option>
                                    <option value="island">Islands</option>
                                    <option value="text">Text</option>
                                </optgroup>
                            </select>
                            <button id="delete-selected" class="btn btn-sm btn-outline-danger" disabled>
                                <i class="fas fa-trash"></i> Delete Selected
                            </button>
                        </div>
                    </div>
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        Click to select • Double-click or <i class="fas fa-edit"></i> for properties • Drag to move • Drag corners/edges to resize
                    </small>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-sm table-hover mb-0" id="elements-table">
                            <thead class="table-light">
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="select-all" class="form-check-input">
                                    </th>
                                    <th width="40">Color</th>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th width="100">Position</th>
                                    <th width="80">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="element-list">
                                <tr>
                                    <td colspan="6" class="text-muted text-center py-3">
                                        <i class="fas fa-info-circle"></i> Elements you add to the canvas will appear here
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <!-- Pagination Controls -->
                    <div class="card-footer d-flex justify-content-between align-items-center">
                        <div class="text-muted small">
                            Showing <span id="elements-showing">0</span> of <span id="elements-total">0</span> elements
                        </div>
                        <nav>
                            <ul class="pagination pagination-sm mb-0" id="element-pagination">
                                <!-- Pagination buttons will be generated by JavaScript -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Load Template Modal -->
<div class="modal fade" id="loadTemplateModal" tabindex="-1">
    <div class="modal-dialog" style="z-index: 1070;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Load Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="templateSelect" class="form-label">Select Template</label>
                    <select class="form-select" id="templateSelect">
                        <option value="">Choose a template...</option>
                        <?php foreach ($templates as $template): ?>
                            <option value="<?= $template->id ?>"><?= htmlspecialchars($template->name) ?>
                                (<?= ucfirst($template->template_type) ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    Loading a template will replace your current layout. Make sure to save first!
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="applyTemplate()">Load Template</button>
            </div>
        </div>
    </div>
</div>

<script>
// VERSION CONTROL: Check if user needs to refresh
const CURRENT_VERSION = '<?php echo LAYOUT_DESIGNER_VERSION; ?>';
const STORED_VERSION = localStorage.getItem('layoutDesignerVersion');

if (STORED_VERSION && STORED_VERSION !== CURRENT_VERSION) {
    console.log('New version detected! Old:', STORED_VERSION, 'New:', CURRENT_VERSION);

    // Create a visible banner notification
    const banner = document.createElement('div');
    banner.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        text-align: center;
        font-size: 18px;
        font-weight: bold;
        z-index: 999999;
        box-shadow: 0 4px 6px rgba(0,0,0,0.3);
        animation: slideDown 0.5s ease-out;
    `;
    banner.innerHTML = `
        <div style="max-width: 800px; margin: 0 auto;">
            <i class="fas fa-sync-alt" style="margin-right: 10px; animation: spin 2s linear infinite;"></i>
            Layout Designer Updated to v${CURRENT_VERSION}
            <br>
            <span style="font-size: 14px; font-weight: normal; margin-top: 5px; display: inline-block;">
                Reloading in <span id="countdown">3</span> seconds to apply changes...
            </span>
        </div>
    `;

    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideDown {
            from { transform: translateY(-100%); }
            to { transform: translateY(0); }
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(style);
    document.body.appendChild(banner);

    // Countdown timer
    let countdown = 3;
    const countdownEl = document.getElementById('countdown');
    const timer = setInterval(() => {
        countdown--;
        if (countdownEl) countdownEl.textContent = countdown;
        if (countdown <= 0) {
            clearInterval(timer);
            localStorage.setItem('layoutDesignerVersion', CURRENT_VERSION);
            location.reload(true); // Force reload from server
        }
    }, 1000);
} else {
    localStorage.setItem('layoutDesignerVersion', CURRENT_VERSION);
}

/*
=============================================================================
IMPORTANT: THIS FILE CONTAINS TWO SEPARATE MANIPULATION SYSTEMS
=============================================================================

1. GRID ELEMENT SYSTEM (DOM-based):
   - Variables: selectedElement, isDragging, dragOffset, etc.
   - Elements: Parking spaces, buildings, stages, etc.
   - Positioning: DOM elements with CSS positioning
   - Handles: Resize handles attached to DOM elements
   - Functions: selectElement(), startDrag(), resizeElement(), etc.

2. CANVAS DRAWING SYSTEM (Canvas-based):
   - Variables: selectedDrawing, isDraggingDrawing, drawingDragArea, etc.
   - Elements: Roads, islands, rectangles, circles drawn on canvas
   - Positioning: Canvas coordinates with zoom scaling
   - Handles: Handle container positioned over canvas
   - Functions: selectDrawing(), startDrawingDrag(), resizeDrawing(), etc.

NEVER mix variables or functions between these two systems!
Each system has its own coordinate system and event handling.
=============================================================================
*/

// BACKUP POINT: 2025-01-02 - Before investigating text tool selection issue
// Canvas group dragging fixed, reconnectGroupElements() updated for mixed groups

const layoutId = <?= $layout ? $layout->id : 'null' ?>;
const showId = <?= $show->id ?>;
const categories = <?= json_encode($categories) ?>;

// GRID ELEMENT SYSTEM VARIABLES
let selectedElement = null;
let selectedElements = []; // Multi-selection support
let elementCounter = 0;
let isDragging = false;
let dragOffset = { x: 0, y: 0 };
// Drag intent tracking to prevent accidental history on simple clicks
let dragStartMouse = { x: 0, y: 0 };
let dragStartElementPos = { x: 0, y: 0 };
let movedDuringDrag = false;
const DRAG_PIXEL_THRESHOLD = 3; // minimum pixels before considering it a move

// SHARED VARIABLES
let zoomLevel = 1;
let layoutElements = [];

// CANVAS DRAWING SYSTEM VARIABLES
let drawingMode = false;
let currentDrawingTool = null;
let drawingColor = '#666666';
let drawingWidth = 10;
let drawingFill = false;
let isDrawing = false;
let drawingStartPoint = null;
let drawings = [];
let drawingCounter = 0;
let selectedDrawing = null;
let selectedDrawings = []; // Multi-selection support for drawings
let drawingHandleContainer = null;
let isDraggingCanvasDrawing = false; // Flag to prevent click interference during drag

// Undo/Redo History System
let history = [];
let historyIndex = -1;
const MAX_HISTORY = 50; // Maximum number of undo steps

// Initialize existing elements
<?php if (!empty($elements)): ?>
    layoutElements = <?= json_encode($elements) ?>;

    // Parse properties field from JSON string to object for each element
    layoutElements.forEach(element => {
        if (element.properties && typeof element.properties === 'string') {
            try {
                element.properties = JSON.parse(element.properties);
            } catch (e) {
                console.warn('Failed to parse properties for element:', element.id, element.properties);
                element.properties = {};
            }
        } else if (!element.properties) {
            element.properties = {};
        }
    });
<?php endif; ?>

// Initialize existing drawings
<?php if (isset($drawings) && !empty($drawings)): ?>
    drawings = <?= json_encode($drawings) ?>;
<?php endif; ?>

// Groups will be initialized after variable declaration

document.addEventListener('DOMContentLoaded', function() {
    initializeDesigner();

    // Initialize drawing system
    initializeDrawingCanvas();

    // Fix any elements with missing or white colors
    fixElementColors();

    // Reconnect group element references if groups were loaded
    <?php if (isset($groups) && !empty($groups)): ?>
    reconnectGroupElements();
    <?php endif; ?>

    // Initialize drawing counter based on existing drawings to prevent ID collisions
    if (drawings && drawings.length > 0) {
        const maxId = Math.max(...drawings.map(d => {
            const match = d.id.match(/draw_(\d+)/);
            return match ? parseInt(match[1]) : 0;
        }));
        drawingCounter = maxId;

    }

    renderElements();
    redrawCanvas(); // Draw saved canvas drawings on page load
    updateStats();
    updateElementList();

    // Initialize history with current state
    saveToHistory('Initial state');

    // Set up element list event listeners
    setupElementListListeners();

    // Set up group element click handlers
    setupGroupElementClickHandlers();

    // Set up draggable properties panel
    setupDraggablePropertiesPanel();

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.toolbar-actions .dropdown-container')) {
            closeAllDropdowns();
        }
    });
});

// =============================================================================
// ALIGNMENT AND GROUPING SYSTEM
// =============================================================================

// Group management variables
let elementGroups = [];
let groupCounter = 0;

// Element list pagination variables
let currentPage = 1;
const elementsPerPage = 20;

// Initialize existing groups from PHP
<?php if (isset($groups) && !empty($groups)): ?>
elementGroups = <?= json_encode($groups) ?>;
// Update group counter to avoid ID conflicts
if (elementGroups.length > 0) {
    groupCounter = Math.max(...elementGroups.map(g => g.id));
}


// Fix group element references after layout elements are loaded
function reconnectGroupElements() {
    elementGroups.forEach(group => {
        group.elements.forEach(groupElement => {
            if (groupElement.type === 'grid') {
                // Find the current element in layoutElements by ID
                const currentElement = layoutElements.find(el => el.id === groupElement.id);
                if (currentElement) {
                    // Update the reference to point to the current element
                    groupElement.element = currentElement;

                }
            } else if (groupElement.type === 'canvas') {
                // Find the current drawing in drawings array by ID
                const currentDrawing = drawings.find(drawing => drawing.id === groupElement.id);
                if (currentDrawing) {
                    // Update the reference to point to the current drawing
                    groupElement.element = currentDrawing;

                }
            }
        });
    });


    // Update group manager display
    updateGroupManager();
}
<?php endif; ?>

// Selection processing flag to prevent double-firing
let isProcessingSelection = false;

// Group drag tracking
let groupDragStartPositions = [];
let groupDragStartDrawings = [];

// Dropdown management functions
function toggleDropdown(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    const isOpen = dropdown.classList.contains('open');

    // Close all dropdowns first
    closeAllDropdowns();

    // Open this dropdown if it wasn't already open
    if (!isOpen) {
        dropdown.classList.add('open');
        updateDropdownStates();
    }
}

function closeAllDropdowns() {
    document.querySelectorAll('.toolbar-actions .dropdown-container').forEach(dropdown => {
        dropdown.classList.remove('open');
    });
}

function updateDropdownStates() {
    const selectedElements = getSelectedElements();
    const hasSelection = selectedElements.length > 0;
    const hasMultipleSelection = selectedElements.length > 1;

    // Update align dropdown button state
    const alignBtn = document.querySelector('.toolbar-actions #align-dropdown .dropdown-btn');
    if (hasMultipleSelection) {
        alignBtn.classList.remove('disabled');
        alignBtn.title = `Align ${selectedElements.length} selected elements`;
    } else {
        alignBtn.classList.add('disabled');
        alignBtn.title = 'Select multiple elements to align';
    }

    // Update group dropdown button state
    const groupBtn = document.querySelector('.toolbar-actions #group-dropdown .dropdown-btn');
    if (hasSelection) {
        groupBtn.classList.remove('disabled');
        groupBtn.title = hasMultipleSelection ? `Group ${selectedElements.length} elements` : 'Manage groups';
    } else {
        groupBtn.classList.add('disabled');
        groupBtn.title = 'Select elements to group';
    }
}

function getSelectedElements() {
    // Return all selected elements from both systems
    const selected = [];

    // Add all selected grid elements
    selected.push(...selectedElements);

    // Add all selected canvas drawings
    selected.push(...selectedDrawings);

    // If no multi-selection, fall back to single selection
    if (selected.length === 0) {
        if (selectedElement) {
            selected.push(selectedElement);
        }
        if (selectedDrawing) {
            selected.push(selectedDrawing);
        }
    }

    return selected;
}

// Multi-selection functions
function addToSelection(element, isDrawing = false) {
    if (isDrawing) {
        if (!selectedDrawings.includes(element)) {
            selectedDrawings.push(element);
        }
    } else {
        if (!selectedElements.includes(element)) {
            selectedElements.push(element);
        }
    }
    updateSelectionDisplay();
}

function removeFromSelection(element, isDrawing = false) {
    if (isDrawing) {
        const index = selectedDrawings.indexOf(element);
        if (index > -1) {
            selectedDrawings.splice(index, 1);
        }
    } else {
        const index = selectedElements.indexOf(element);
        if (index > -1) {
            selectedElements.splice(index, 1);
        }
    }
    updateSelectionDisplay();
}

function clearSelection() {
    // Update group data if an element was selected from group manager
    updateGroupDataAfterModification();

    selectedElements = [];
    selectedDrawings = [];
    selectedElement = null;
    selectedDrawing = null;

    // Hide size indicator when clearing selection
    hideSizeIndicator();

    // Remove all resize handles from grid elements
    removeAllResizeHandles();

    // Remove all handles from canvas drawings (including drag area overlay)
    removeDrawingHandles();

    // Clear visual selection from all grid elements
    document.querySelectorAll('.parking-space, .building, .entrance, .exit, .amenity').forEach(el => {
        el.classList.remove('selected');
    });

    // Clear group manager selection highlighting
    document.querySelectorAll('.group-element-item').forEach(item => {
        item.classList.remove('selected');
    });

    // Reset canvas cursor to default (fixes stuck 'move' cursor)
    const designCanvas = document.getElementById('design-canvas');
    if (designCanvas) {
        designCanvas.style.cursor = 'default';
    }

    updateSelectionDisplay();
    updateDropdownStates();
}

function isElementSelected(element, isDrawing = false) {
    if (isDrawing) {
        // Compare by reference OR by stable id to handle reconnected/group references
        const inMulti = selectedDrawings.includes(element) || selectedDrawings.some(d => d && element && d.id === element.id);
        const isSingle = selectedDrawing === element || (selectedDrawing && element && selectedDrawing.id === element.id);
        return inMulti || isSingle;
    } else {
        return selectedElements.includes(element) || selectedElement === element;
    }
}

function updateSelectionDisplay() {
    const totalSelected = selectedElements.length + selectedDrawings.length;
    const singleSelected = (selectedElement ? 1 : 0) + (selectedDrawing ? 1 : 0);
    const grandTotal = totalSelected + (selectedElements.length === 0 && selectedDrawings.length === 0 ? singleSelected : 0);

    const counter = document.getElementById('selection-counter');
    const countSpan = document.getElementById('selection-count');

    if (totalSelected > 1 || (totalSelected === 1 && singleSelected === 1)) {
        countSpan.textContent = Math.max(totalSelected, grandTotal);
        counter.classList.add('show');
    } else {
        counter.classList.remove('show');
    }

    // Update visual styling for grid elements
    document.querySelectorAll('.layout-element').forEach(div => {
        div.classList.remove('selected', 'multi-selected');
    });

    // Style multi-selected elements
    selectedElements.forEach(element => {
        const div = document.getElementById('element_' + element.id);
        if (div) {
            div.classList.add('multi-selected');
        }
    });

    // Style single selected element (only if no multi-selection)
    if (selectedElement && selectedElements.length === 0) {
        const div = document.getElementById('element_' + selectedElement.id);
        if (div) {
            div.classList.add('selected');
        }
    }

    // Update canvas drawing selection styling
    redrawCanvas();

    // Canvas drawings use visual selection styling only (like grid elements)
    // No separate drag area needed - user can drag any selected drawing to move the group

    updateDropdownStates();
}

// Group selection functions
function findElementGroup(element) {
    const elementId = element.id || element.drawingId;
    const isDrawing = element.points !== undefined;

    for (let group of elementGroups) {
        const found = group.elements.find(el =>
            el.id === elementId &&
            ((el.type === 'canvas') === isDrawing)
        );
        if (found) {
            return group;
        }
    }
    return null;
}

function selectEntireGroup(group) {


    // Clear current selections
    clearSelection();

    // Remove any single-drawing UI (handles/overlays) when switching to group mode
    removeDrawingHandles();
    hideRoadEditor();
    hideGeneralDrawingEditor();

    // Select all elements in the group
    group.elements.forEach(groupElement => {
        if (groupElement.element) {
            if (groupElement.type === 'grid') {
                selectedElements.push(groupElement.element);

            } else if (groupElement.type === 'canvas') {
                selectedDrawings.push(groupElement.element);

            }
        }
    });

    updateSelectionDisplay();

    // Show group selection message
    const totalSelected = selectedElements.length + selectedDrawings.length;
    if (totalSelected > 1) {


        // Update selection counter to show group name
        const counter = document.getElementById('selection-counter');
        const countSpan = document.getElementById('selection-count');
        if (counter && countSpan) {
            countSpan.textContent = totalSelected;
            counter.innerHTML = `
                <i class="fas fa-object-group"></i>
                <span id="selection-count">${totalSelected}</span> elements selected
                <small style="margin-left: 10px; opacity: 0.8;">Group: ${group.name}</small>
            `;
            counter.classList.add('show');
        }
    }
}

function selectEntireGroupMulti(group) {


    // Add all elements in the group to existing selection (don't clear)
    group.elements.forEach(groupElement => {
        if (groupElement.element) {
            if (groupElement.type === 'grid') {
                // Check if element is already selected to avoid duplicates
                if (!selectedElements.includes(groupElement.element)) {
                    selectedElements.push(groupElement.element);

                }
            } else if (groupElement.type === 'canvas') {
                // Check if element is already selected to avoid duplicates
                if (!selectedDrawings.includes(groupElement.element)) {
                    selectedDrawings.push(groupElement.element);

                }
            }
        }
    });

    updateSelectionDisplay();

    // Update selection counter for multi-group selection
    const totalSelected = selectedElements.length + selectedDrawings.length;
    if (totalSelected > 1) {


        // Update selection counter to show multi-group selection
        const counter = document.getElementById('selection-counter');
        const countSpan = document.getElementById('selection-count');
        if (counter && countSpan) {
            countSpan.textContent = totalSelected;
            counter.innerHTML = `
                <i class="fas fa-object-group"></i>
                <span id="selection-count">${totalSelected}</span> elements selected
                <small style="margin-left: 10px; opacity: 0.8;">Multiple groups</small>
            `;
            counter.classList.add('show');
        }
    }
}

function selectAllElements() {
    // Select all grid elements
    selectedElements = [...layoutElements];

    // Select all canvas drawings
    selectedDrawings = [...drawings];

    // Clear single selections
    selectedElement = null;
    selectedDrawing = null;

    updateSelectionDisplay();

    // Show selection counter
    const totalSelected = selectedElements.length + selectedDrawings.length;
    if (totalSelected > 0) {
        alert(`Selected all ${totalSelected} elements (${selectedElements.length} grid elements, ${selectedDrawings.length} drawings)`);
    }
}

function deleteSelectedElements() {
    const gridCount = selectedElements.length;
    const drawingCount = selectedDrawings.length;
    const totalCount = gridCount + drawingCount;

    if (totalCount === 0) {
        // Check for single selections
        if (selectedElement) {
            deleteElement(selectedElement.id);
            return;
        }
        if (selectedDrawing) {
            if (selectedDrawing.tool === 'road') {
                deleteSelectedRoad();
            } else {
                deleteSelectedDrawing();
            }
            return;
        }
        return;
    }

    if (confirm(`Delete ${totalCount} selected elements? This action cannot be undone.`)) {
        // Delete grid elements
        selectedElements.forEach(element => {
            const index = layoutElements.findIndex(el => el.id === element.id);
            if (index > -1) {
                layoutElements.splice(index, 1);
            }
        });

        // Delete canvas drawings
        selectedDrawings.forEach(drawing => {
            const index = drawings.findIndex(d => d.id === drawing.id);
            if (index > -1) {
                drawings.splice(index, 1);
            }
        });

        // Clear selections
        clearSelection();

        // Update displays
        renderElements();
        redrawCanvas();
        updateStats();
        updateElementList();

        saveToHistory(`Delete ${totalCount} elements`);

        alert(`Deleted ${totalCount} elements`);
    }
}

// Alignment functions
function alignElements(alignType) {
    const elements = getSelectedElements();
    if (elements.length < 2) {
        alert('Please select at least 2 elements to align');
        return;
    }

    closeAllDropdowns();

    // Check if we have groups selected - if so, use group-aware alignment
    const selectedGroups = getSelectedGroups();
    if (selectedGroups.length > 0) {
        alignGroupsAndElements(alignType);
    } else {
        // No groups selected, use mixed alignment logic
        alignMixedElements(elements, alignType);
    }

    // Update displays
    renderElements();
    redrawCanvas();
    saveToHistory(`Align elements ${alignType}`);
}

function alignGridElements(elements, alignType) {
    if (elements.length < 2) return;

    // Use first element as reference
    const reference = elements[0];

    switch (alignType) {
        case 'left':
            elements.slice(1).forEach(el => {
                el.x_position = reference.x_position;
            });
            break;
        case 'right':
            const refRight = reference.x_position + reference.width;
            elements.slice(1).forEach(el => {
                el.x_position = refRight - el.width;
            });
            break;
        case 'center-h':
            const refCenterX = reference.x_position + reference.width / 2;
            elements.slice(1).forEach(el => {
                el.x_position = refCenterX - el.width / 2;
            });
            break;
        case 'top':
            elements.slice(1).forEach(el => {
                el.y_position = reference.y_position;
            });
            break;
        case 'bottom':
            const refBottom = reference.y_position + reference.height;
            elements.slice(1).forEach(el => {
                el.y_position = refBottom - el.height;
            });
            break;
        case 'center-v':
            const refCenterY = reference.y_position + reference.height / 2;
            elements.slice(1).forEach(el => {
                el.y_position = refCenterY - el.height / 2;
            });
            break;
    }
}

function alignCanvasDrawings(drawings, alignType) {
    if (drawings.length < 2) return;

    // Use first drawing as reference
    const reference = drawings[0];
    const refBounds = getDrawingBounds(reference);

    drawings.slice(1).forEach(drawing => {
        const bounds = getDrawingBounds(drawing);
        let deltaX = 0, deltaY = 0;

        switch (alignType) {
            case 'left':
                deltaX = refBounds.left - bounds.left;
                break;
            case 'right':
                deltaX = refBounds.right - bounds.right;
                break;
            case 'center-h':
                const refCenterX = refBounds.left + refBounds.width / 2;
                const centerX = bounds.left + bounds.width / 2;
                deltaX = refCenterX - centerX;
                break;
            case 'top':
                deltaY = refBounds.top - bounds.top;
                break;
            case 'bottom':
                deltaY = refBounds.bottom - bounds.bottom;
                break;
            case 'center-v':
                const refCenterY = refBounds.top + refBounds.height / 2;
                const centerY = bounds.top + bounds.height / 2;
                deltaY = refCenterY - centerY;
                break;
        }

        // Move drawing by delta
        if (deltaX !== 0 || deltaY !== 0) {
            drawing.points.forEach(point => {
                point.x += deltaX;
                point.y += deltaY;
            });
        }
    });
}

// Get visual bounds for alignment (accounting for borders)
function getVisualBounds(element) {
    if (element.x_position !== undefined) {
        // Grid element - account for border width (2px normal, 3px when selected)
        const borderWidth = selectedElements.includes(element) ? 3 : 2;
        return {
            x: element.x_position,
            y: element.y_position,
            width: element.width,
            height: element.height,
            // Visual bounds account for border
            left: element.x_position - borderWidth,
            top: element.y_position - borderWidth,
            right: element.x_position + element.width + borderWidth,
            bottom: element.y_position + element.height + borderWidth,
            centerX: element.x_position + element.width / 2,
            centerY: element.y_position + element.height / 2
        };
    } else {
        // Canvas drawing - get bounds without padding
        if (!element || element.points.length < 1) return null;
        if (element.tool !== 'text' && element.points.length < 2) return null;

        let minX, minY, maxX, maxY, centerX, centerY;

        switch (element.tool) {
            case 'circle':
                const ellipseCenter = element.points[0];
                const ellipseEdge = element.points[1];
                const radiusX = Math.abs(ellipseEdge.x - ellipseCenter.x);
                const radiusY = Math.abs(ellipseEdge.y - ellipseCenter.y);
                minX = ellipseCenter.x - radiusX;
                minY = ellipseCenter.y - radiusY;
                maxX = ellipseCenter.x + radiusX;
                maxY = ellipseCenter.y + radiusY;
                centerX = ellipseCenter.x;
                centerY = ellipseCenter.y;
                break;
            case 'text':
                const textX = element.points[0].x;
                const textY = element.points[0].y;
                const fontSize = element.style.fontSize || 16;
                const tempCanvas = document.createElement('canvas');
                const tempCtx = tempCanvas.getContext('2d');
                tempCtx.font = `${fontSize}px ${element.style.fontFamily || 'Arial, sans-serif'}`;
                const textMetrics = tempCtx.measureText(element.text || '');
                const textWidth = textMetrics.width;
                const textHeight = fontSize;
                minX = textX;
                minY = textY;
                maxX = textX + textWidth;
                maxY = textY + textHeight;
                centerX = textX + textWidth / 2;
                centerY = textY + textHeight / 2;
                break;
            default:
                minX = Infinity;
                minY = Infinity;
                maxX = -Infinity;
                maxY = -Infinity;
                element.points.forEach(point => {
                    minX = Math.min(minX, point.x);
                    minY = Math.min(minY, point.y);
                    maxX = Math.max(maxX, point.x);
                    maxY = Math.max(maxY, point.y);
                });
                centerX = (minX + maxX) / 2;
                centerY = (minY + maxY) / 2;
                break;
        }

        // Return bounds WITHOUT padding for alignment
        return {
            x: minX,
            y: minY,
            width: maxX - minX,
            height: maxY - minY,
            left: minX,
            top: minY,
            right: maxX,
            bottom: maxY,
            centerX: centerX,
            centerY: centerY
        };
    }
}

// Mixed alignment function for both grid elements and canvas drawings
function alignMixedElements(elements, alignType) {
    if (elements.length < 2) return;

    // Use first element as reference, regardless of type
    const reference = elements[0];
    let refBounds;

    // Get reference bounds based on element type
    if (reference.x_position !== undefined) {
        // Grid element - account for border width (2px normal, 3px when selected)
        const borderWidth = selectedElements.includes(reference) ? 3 : 2;
        refBounds = {
            x: reference.x_position,
            y: reference.y_position,
            width: reference.width,
            height: reference.height,
            // Visual bounds account for border
            left: reference.x_position - borderWidth,
            top: reference.y_position - borderWidth,
            right: reference.x_position + reference.width + borderWidth,
            bottom: reference.y_position + reference.height + borderWidth,
            centerX: reference.x_position + reference.width / 2,
            centerY: reference.y_position + reference.height / 2
        };
    } else {
        // Canvas drawing - use bounds with padding
        const bounds = getDrawingBounds(reference);
        refBounds = {
            x: bounds.x,
            y: bounds.y,
            width: bounds.width,
            height: bounds.height,
            left: bounds.x,
            top: bounds.y,
            right: bounds.x + bounds.width,
            bottom: bounds.y + bounds.height,
            centerX: bounds.centerX,
            centerY: bounds.centerY
        };
    }

    // Align all other elements to the reference
    elements.slice(1).forEach(element => {
        let elementBounds;

        // Get element bounds based on type
        if (element.x_position !== undefined) {
            // Grid element - account for border width (2px normal, 3px when selected)
            const borderWidth = selectedElements.includes(element) ? 3 : 2;
            elementBounds = {
                x: element.x_position,
                y: element.y_position,
                width: element.width,
                height: element.height,
                // Visual bounds account for border
                left: element.x_position - borderWidth,
                top: element.y_position - borderWidth,
                right: element.x_position + element.width + borderWidth,
                bottom: element.y_position + element.height + borderWidth,
                centerX: element.x_position + element.width / 2,
                centerY: element.y_position + element.height / 2
            };
        } else {
            // Canvas drawing - use bounds with padding
            const bounds = getDrawingBounds(element);
            elementBounds = {
                x: bounds.x,
                y: bounds.y,
                width: bounds.width,
                height: bounds.height,
                left: bounds.x,
                top: bounds.y,
                right: bounds.x + bounds.width,
                bottom: bounds.y + bounds.height,
                centerX: bounds.centerX,
                centerY: bounds.centerY
            };
        }

        let deltaX = 0, deltaY = 0;

        switch (alignType) {
            case 'left':
                deltaX = refBounds.left - elementBounds.left;
                break;
            case 'right':
                deltaX = refBounds.right - elementBounds.right;
                break;
            case 'center-h':
                deltaX = refBounds.centerX - elementBounds.centerX;
                break;
            case 'top':
                deltaY = refBounds.top - elementBounds.top;
                break;
            case 'bottom':
                deltaY = refBounds.bottom - elementBounds.bottom;
                break;
            case 'center-v':
                deltaY = refBounds.centerY - elementBounds.centerY;
                break;
        }

        // Apply the delta based on element type
        if (deltaX !== 0 || deltaY !== 0) {
            if (element.x_position !== undefined) {
                // Grid element - update position
                element.x_position += deltaX;
                element.y_position += deltaY;
            } else {
                // Canvas drawing - update all points
                element.points.forEach(point => {
                    point.x += deltaX;
                    point.y += deltaY;
                });
            }
        }
    });
}

// Group-aware alignment function
function alignGroupsAndElements(alignType) {
    const selectedGroups = getSelectedGroups();
    const individualElements = [];

    // Get individual elements that are not part of fully selected groups
    selectedElements.forEach(element => {
        const group = findElementGroup(element);
        if (!group || !selectedGroups.includes(group)) {
            individualElements.push(element);
        }
    });

    // Create alignment units (groups + individual elements)
    const alignmentUnits = [];

    // Add groups as units
    selectedGroups.forEach(group => {
        const bounds = getGroupBounds(group);
        if (bounds) {
            alignmentUnits.push({
                type: 'group',
                group: group,
                bounds: bounds
            });
        }
    });

    // Add individual elements as units
    individualElements.forEach(element => {
        let bounds;
        if (element.x_position !== undefined) {
            // Grid element - account for border width
            const borderWidth = selectedElements.includes(element) ? 3 : 2;
            bounds = {
                left: element.x_position - borderWidth,
                top: element.y_position - borderWidth,
                right: element.x_position + element.width + borderWidth,
                bottom: element.y_position + element.height + borderWidth,
                width: element.width,
                height: element.height,
                centerX: element.x_position + element.width / 2,
                centerY: element.y_position + element.height / 2
            };
        } else {
            // Canvas drawing
            const drawingBounds = getDrawingBounds(element);
            bounds = {
                left: drawingBounds.x,
                top: drawingBounds.y,
                right: drawingBounds.x + drawingBounds.width,
                bottom: drawingBounds.y + drawingBounds.height,
                width: drawingBounds.width,
                height: drawingBounds.height,
                centerX: drawingBounds.centerX,
                centerY: drawingBounds.centerY
            };
        }

        alignmentUnits.push({
            type: 'element',
            element: element,
            bounds: bounds,
            isGrid: element.x_position !== undefined
        });
    });

    if (alignmentUnits.length < 2) {
        alert('Please select at least 2 groups/elements to align');
        return;
    }

    // Use the first unit as the reference for alignment
    const reference = alignmentUnits[0];

    // Align all other units to the reference
    for (let i = 1; i < alignmentUnits.length; i++) {
        const unit = alignmentUnits[i];
        let deltaX = 0, deltaY = 0;

        switch (alignType) {
            case 'left':
                deltaX = reference.bounds.left - unit.bounds.left;
                break;
            case 'center-h':
                deltaX = reference.bounds.centerX - unit.bounds.centerX;
                break;
            case 'right':
                deltaX = reference.bounds.right - unit.bounds.right;
                break;
            case 'top':
                deltaY = reference.bounds.top - unit.bounds.top;
                break;
            case 'center-v':
                deltaY = reference.bounds.centerY - unit.bounds.centerY;
                break;
            case 'bottom':
                deltaY = reference.bounds.bottom - unit.bounds.bottom;
                break;
        }

        // Apply the alignment movement
        if (unit.type === 'group') {
            // Move all elements in the group
            unit.group.elements.forEach(groupElement => {
                if (groupElement.type === 'grid' && groupElement.element) {
                    if (deltaX !== 0) groupElement.element.x_position += deltaX;
                    if (deltaY !== 0) groupElement.element.y_position += deltaY;
                }
            });
        } else {
            // Move individual element
            if (unit.isGrid) {
                // Grid element
                if (deltaX !== 0) unit.element.x_position += deltaX;
                if (deltaY !== 0) unit.element.y_position += deltaY;
            } else {
                // Canvas drawing - move all points
                unit.element.points.forEach(point => {
                    if (deltaX !== 0) point.x += deltaX;
                    if (deltaY !== 0) point.y += deltaY;
                });
            }
        }
    }
}

// Distribution functions
function distributeElements(direction) {
    const elements = getSelectedElements();

    // For no-spacing distribution, we need at least 2 elements
    const minElements = (direction === 'horizontal-no-spacing' || direction === 'vertical-no-spacing') ? 2 : 3;

    if (elements.length < minElements) {
        alert(`Please select at least ${minElements} elements to distribute`);
        return;
    }

    closeAllDropdowns();

    // Check if we have groups selected - if so, use group-aware distribution
    const selectedGroups = getSelectedGroups();
    if (selectedGroups.length > 0) {
        distributeGroupsAndElements(direction);
    } else {
        // Handle no-spacing distribution for mixed elements
        if (direction === 'horizontal-no-spacing' || direction === 'vertical-no-spacing') {
            distributeMixedElementsNoSpacing(elements, direction);
        } else {
            // Regular distribution - check if we have mixed elements
            const gridElements = elements.filter(el => el.x_position !== undefined);
            const canvasDrawings = elements.filter(el => el.points !== undefined);

            // If we have both types, use mixed distribution
            if (gridElements.length > 0 && canvasDrawings.length > 0) {
                distributeMixedElements(elements, direction);
            } else if (gridElements.length > 2) {
                // Only grid elements
                distributeGridElements(gridElements, direction);
            } else if (canvasDrawings.length > 2) {
                // Only canvas drawings
                distributeCanvasDrawings(canvasDrawings, direction);
            }
        }
    }

    // Update displays
    renderElements();
    redrawCanvas();
    saveToHistory(`Distribute elements ${direction}`);
}

function distributeGridElements(elements, direction) {
    if (elements.length < 3) return;

    if (direction === 'horizontal') {
        // Sort by x position
        elements.sort((a, b) => a.x_position - b.x_position);

        const first = elements[0];
        const last = elements[elements.length - 1];
        const totalSpace = (last.x_position + last.width) - first.x_position;
        const totalElementWidth = elements.reduce((sum, el) => sum + el.width, 0);
        const availableSpace = totalSpace - totalElementWidth;
        const gap = availableSpace / (elements.length - 1);

        let currentX = first.x_position + first.width;
        for (let i = 1; i < elements.length - 1; i++) {
            currentX += gap;
            elements[i].x_position = currentX;
            currentX += elements[i].width;
        }
    } else {
        // Sort by y position
        elements.sort((a, b) => a.y_position - b.y_position);

        const first = elements[0];
        const last = elements[elements.length - 1];
        const totalSpace = (last.y_position + last.height) - first.y_position;
        const totalElementHeight = elements.reduce((sum, el) => sum + el.height, 0);
        const availableSpace = totalSpace - totalElementHeight;
        const gap = availableSpace / (elements.length - 1);

        let currentY = first.y_position + first.height;
        for (let i = 1; i < elements.length - 1; i++) {
            currentY += gap;
            elements[i].y_position = currentY;
            currentY += elements[i].height;
        }
    }
}

// Helper function to get selected groups
function getSelectedGroups() {
    const selectedGroups = [];
    const processedGroups = new Set();

    // Check each selected element to see if it belongs to a group
    selectedElements.forEach(element => {
        const group = findElementGroup(element);
        if (group && !processedGroups.has(group.id)) {
            // Check if ALL elements of this group are selected
            const groupElements = group.elements.filter(ge => ge.type === 'grid').map(ge => ge.element);
            const allSelected = groupElements.every(ge => selectedElements.includes(ge));

            if (allSelected) {
                selectedGroups.push(group);
                processedGroups.add(group.id);
            }
        }
    });

    return selectedGroups;
}

// Helper function to calculate group bounds
function getGroupBounds(group) {
    const gridElements = group.elements.filter(ge => ge.type === 'grid').map(ge => ge.element);

    if (gridElements.length === 0) return null;

    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

    gridElements.forEach(element => {
        minX = Math.min(minX, element.x_position);
        minY = Math.min(minY, element.y_position);
        maxX = Math.max(maxX, element.x_position + element.width);
        maxY = Math.max(maxY, element.y_position + element.height);
    });

    return {
        left: minX,
        top: minY,
        right: maxX,
        bottom: maxY,
        width: maxX - minX,
        height: maxY - minY,
        centerX: minX + (maxX - minX) / 2,
        centerY: minY + (maxY - minY) / 2
    };
}

// Group-aware distribution function
function distributeGroupsAndElements(direction) {
    const selectedGroups = getSelectedGroups();
    const individualElements = [];

    // Get individual elements that are not part of fully selected groups
    selectedElements.forEach(element => {
        const group = findElementGroup(element);
        if (!group || !selectedGroups.includes(group)) {
            individualElements.push(element);
        }
    });

    // Create distribution units (groups + individual elements)
    const distributionUnits = [];

    // Add groups as units
    selectedGroups.forEach(group => {
        const bounds = getGroupBounds(group);
        if (bounds) {
            distributionUnits.push({
                type: 'group',
                group: group,
                bounds: bounds
            });
        }
    });

    // Determine if we need visual bounds (no padding) for no-spacing distribution
    const useVisualBounds = (direction === 'horizontal-no-spacing' || direction === 'vertical-no-spacing');

    // Add individual elements as units (both grid and canvas)
    individualElements.forEach(element => {
        let bounds;

        if (element.x_position !== undefined) {
            // Grid element
            bounds = {
                left: element.x_position,
                top: element.y_position,
                right: element.x_position + element.width,
                bottom: element.y_position + element.height,
                width: element.width,
                height: element.height,
                centerX: element.x_position + element.width / 2,
                centerY: element.y_position + element.height / 2
            };
        } else {
            // Canvas drawing - use visual bounds for no-spacing, regular bounds otherwise
            const drawingBounds = useVisualBounds ? getDrawingVisualBounds(element) : getDrawingBounds(element);
            bounds = {
                left: useVisualBounds ? drawingBounds.left : drawingBounds.x,
                top: useVisualBounds ? drawingBounds.top : drawingBounds.y,
                right: useVisualBounds ? drawingBounds.right : (drawingBounds.x + drawingBounds.width),
                bottom: useVisualBounds ? drawingBounds.bottom : (drawingBounds.y + drawingBounds.height),
                width: drawingBounds.width,
                height: drawingBounds.height,
                centerX: drawingBounds.centerX,
                centerY: drawingBounds.centerY
            };
        }

        distributionUnits.push({
            type: 'element',
            element: element,
            bounds: bounds,
            isGrid: element.x_position !== undefined
        });
    });

    const minUnits = (direction === 'horizontal-no-spacing' || direction === 'vertical-no-spacing') ? 2 : 3;

    if (distributionUnits.length < minUnits) {
        alert(`Please select at least ${minUnits} groups/elements to distribute`);
        return;
    }

    // Distribute the units
    if (direction === 'horizontal') {
        distributeUnitsHorizontally(distributionUnits);
    } else if (direction === 'vertical') {
        distributeUnitsVertically(distributionUnits);
    } else if (direction === 'horizontal-no-spacing') {
        distributeUnitsHorizontallyNoSpacing(distributionUnits);
    } else if (direction === 'vertical-no-spacing') {
        distributeUnitsVerticallyNoSpacing(distributionUnits);
    }
}

// Distribute units (groups and elements) horizontally
function distributeUnitsHorizontally(units) {
    // Sort by left position
    units.sort((a, b) => a.bounds.left - b.bounds.left);

    const first = units[0];
    const last = units[units.length - 1];
    const totalSpace = last.bounds.right - first.bounds.left;
    const totalUnitWidth = units.reduce((sum, unit) => sum + unit.bounds.width, 0);
    const availableSpace = totalSpace - totalUnitWidth;
    const gap = availableSpace / (units.length - 1);

    let currentX = first.bounds.left + first.bounds.width;

    for (let i = 1; i < units.length - 1; i++) {
        currentX += gap;
        const unit = units[i];
        const deltaX = currentX - unit.bounds.left;

        if (unit.type === 'group') {
            // Move all elements in the group
            unit.group.elements.forEach(groupElement => {
                if (groupElement.type === 'grid' && groupElement.element) {
                    groupElement.element.x_position += deltaX;
                } else if (groupElement.type === 'canvas' && groupElement.element) {
                    // Move canvas drawing points
                    groupElement.element.points.forEach(point => {
                        point.x += deltaX;
                    });
                }
            });
        } else {
            // Move individual element
            if (unit.isGrid) {
                unit.element.x_position += deltaX;
            } else {
                // Canvas drawing
                unit.element.points.forEach(point => {
                    point.x += deltaX;
                });
            }
        }

        currentX += unit.bounds.width;
    }
}

// Distribute units (groups and elements) vertically
function distributeUnitsVertically(units) {
    // Sort by top position
    units.sort((a, b) => a.bounds.top - b.bounds.top);

    const first = units[0];
    const last = units[units.length - 1];
    const totalSpace = last.bounds.bottom - first.bounds.top;
    const totalUnitHeight = units.reduce((sum, unit) => sum + unit.bounds.height, 0);
    const availableSpace = totalSpace - totalUnitHeight;
    const gap = availableSpace / (units.length - 1);

    let currentY = first.bounds.top + first.bounds.height;

    for (let i = 1; i < units.length - 1; i++) {
        currentY += gap;
        const unit = units[i];
        const deltaY = currentY - unit.bounds.top;

        if (unit.type === 'group') {
            // Move all elements in the group
            unit.group.elements.forEach(groupElement => {
                if (groupElement.type === 'grid' && groupElement.element) {
                    groupElement.element.y_position += deltaY;
                } else if (groupElement.type === 'canvas' && groupElement.element) {
                    // Move canvas drawing points
                    groupElement.element.points.forEach(point => {
                        point.y += deltaY;
                    });
                }
            });
        } else {
            // Move individual element
            if (unit.isGrid) {
                unit.element.y_position += deltaY;
            } else {
                // Canvas drawing
                unit.element.points.forEach(point => {
                    point.y += deltaY;
                });
            }
        }

        currentY += unit.bounds.height;
    }
}

// Distribute units horizontally with no spacing (edges touching)
function distributeUnitsHorizontallyNoSpacing(units) {
    // Sort by left position
    units.sort((a, b) => a.bounds.left - b.bounds.left);

    // Start from the leftmost unit and stack rightward with no gaps
    let currentX = units[0].bounds.right;

    for (let i = 1; i < units.length; i++) {
        const unit = units[i];
        const deltaX = currentX - unit.bounds.left;

        if (unit.type === 'group') {
            // Move all elements in the group
            unit.group.elements.forEach(groupElement => {
                if (groupElement.type === 'grid' && groupElement.element) {
                    groupElement.element.x_position += deltaX;
                } else if (groupElement.type === 'canvas' && groupElement.element) {
                    // Move canvas drawing points
                    groupElement.element.points.forEach(point => {
                        point.x += deltaX;
                    });
                }
            });
        } else {
            // Move individual element
            if (unit.isGrid) {
                unit.element.x_position += deltaX;
            } else {
                // Canvas drawing
                unit.element.points.forEach(point => {
                    point.x += deltaX;
                });
            }
        }

        // Update currentX to the right edge of this unit (after moving)
        currentX = currentX + unit.bounds.width;
    }
}

// Distribute units vertically with no spacing (edges touching)
function distributeUnitsVerticallyNoSpacing(units) {
    // Sort by top position
    units.sort((a, b) => a.bounds.top - b.bounds.top);

    // Start from the topmost unit and stack downward with no gaps
    let currentY = units[0].bounds.bottom;

    for (let i = 1; i < units.length; i++) {
        const unit = units[i];
        const deltaY = currentY - unit.bounds.top;

        if (unit.type === 'group') {
            // Move all elements in the group
            unit.group.elements.forEach(groupElement => {
                if (groupElement.type === 'grid' && groupElement.element) {
                    groupElement.element.y_position += deltaY;
                } else if (groupElement.type === 'canvas' && groupElement.element) {
                    // Move canvas drawing points
                    groupElement.element.points.forEach(point => {
                        point.y += deltaY;
                    });
                }
            });
        } else {
            // Move individual element
            if (unit.isGrid) {
                unit.element.y_position += deltaY;
            } else {
                // Canvas drawing
                unit.element.points.forEach(point => {
                    point.y += deltaY;
                });
            }
        }

        // Update currentY to the bottom edge of this unit (after moving)
        currentY = currentY + unit.bounds.height;
    }
}

function distributeCanvasDrawings(drawings, direction) {
    if (drawings.length < 3) return;

    // Get bounds for all drawings
    const drawingsWithBounds = drawings.map(drawing => ({
        drawing,
        bounds: getDrawingBounds(drawing)
    }));

    if (direction === 'horizontal') {
        // Sort by left position
        drawingsWithBounds.sort((a, b) => a.bounds.left - b.bounds.left);

        const first = drawingsWithBounds[0];
        const last = drawingsWithBounds[drawingsWithBounds.length - 1];
        const totalSpace = last.bounds.right - first.bounds.left;
        const totalWidth = drawingsWithBounds.reduce((sum, item) => sum + item.bounds.width, 0);
        const availableSpace = totalSpace - totalWidth;
        const gap = availableSpace / (drawingsWithBounds.length - 1);

        let currentX = first.bounds.right;
        for (let i = 1; i < drawingsWithBounds.length - 1; i++) {
            currentX += gap;
            const item = drawingsWithBounds[i];
            const deltaX = currentX - item.bounds.left;

            // Move drawing
            item.drawing.points.forEach(point => {
                point.x += deltaX;
            });

            currentX += item.bounds.width;
        }
    } else {
        // Sort by top position
        drawingsWithBounds.sort((a, b) => a.bounds.top - b.bounds.top);

        const first = drawingsWithBounds[0];
        const last = drawingsWithBounds[drawingsWithBounds.length - 1];
        const totalSpace = last.bounds.bottom - first.bounds.top;
        const totalHeight = drawingsWithBounds.reduce((sum, item) => sum + item.bounds.height, 0);
        const availableSpace = totalSpace - totalHeight;
        const gap = availableSpace / (drawingsWithBounds.length - 1);

        let currentY = first.bounds.bottom;
        for (let i = 1; i < drawingsWithBounds.length - 1; i++) {
            currentY += gap;
            const item = drawingsWithBounds[i];
            const deltaY = currentY - item.bounds.top;

            // Move drawing
            item.drawing.points.forEach(point => {
                point.y += deltaY;
            });

            currentY += item.bounds.height;
        }
    }
}

// Distribute mixed elements with regular spacing (evenly spaced)
function distributeMixedElements(elements, direction) {
    if (elements.length < 3) return;

    // Create units with bounds for all elements (both DOM and canvas)
    const units = elements.map(element => {
        let bounds;

        if (element.x_position !== undefined) {
            // Grid element (DOM)
            bounds = {
                left: element.x_position,
                top: element.y_position,
                right: element.x_position + element.width,
                bottom: element.y_position + element.height,
                width: element.width,
                height: element.height,
                centerX: element.x_position + element.width / 2,
                centerY: element.y_position + element.height / 2
            };
        } else {
            // Canvas drawing - use regular bounds (with padding)
            const drawingBounds = getDrawingBounds(element);
            bounds = {
                left: drawingBounds.x,
                top: drawingBounds.y,
                right: drawingBounds.x + drawingBounds.width,
                bottom: drawingBounds.y + drawingBounds.height,
                width: drawingBounds.width,
                height: drawingBounds.height,
                centerX: drawingBounds.centerX,
                centerY: drawingBounds.centerY
            };
        }

        return {
            element: element,
            bounds: bounds,
            isGrid: element.x_position !== undefined
        };
    });

    if (direction === 'horizontal') {
        // Sort by left position
        units.sort((a, b) => a.bounds.left - b.bounds.left);

        console.log('distributeMixedElements horizontal - units:', units.map(u => ({
            isGrid: u.isGrid,
            left: u.bounds.left,
            right: u.bounds.right,
            width: u.bounds.width
        })));

        // Calculate total space from leftmost to rightmost
        const firstUnit = units[0];
        const lastUnit = units[units.length - 1];
        const totalSpace = lastUnit.bounds.right - firstUnit.bounds.left;

        console.log('Total space:', totalSpace, 'from', firstUnit.bounds.left, 'to', lastUnit.bounds.right);

        // Calculate total width of all elements
        const totalWidth = units.reduce((sum, unit) => sum + unit.bounds.width, 0);

        console.log('Total width of all elements:', totalWidth);

        // Calculate available space for gaps
        const availableSpace = totalSpace - totalWidth;
        const gap = availableSpace / (units.length - 1);

        console.log('Available space:', availableSpace, 'Gap between elements:', gap);

        // Distribute all elements with equal gaps
        let currentX = firstUnit.bounds.left + firstUnit.bounds.width;

        for (let i = 1; i < units.length; i++) {
            currentX += gap;
            const unit = units[i];
            const deltaX = currentX - unit.bounds.left;

            console.log(`Unit ${i}: currentX=${currentX}, unit.left=${unit.bounds.left}, deltaX=${deltaX}`);

            if (unit.isGrid) {
                unit.element.x_position += deltaX;
            } else {
                unit.element.points.forEach(point => {
                    point.x += deltaX;
                });
            }

            currentX += unit.bounds.width;
        }
    } else if (direction === 'vertical') {
        // Sort by top position
        units.sort((a, b) => a.bounds.top - b.bounds.top);

        // Calculate total space from topmost to bottommost
        const firstUnit = units[0];
        const lastUnit = units[units.length - 1];
        const totalSpace = lastUnit.bounds.bottom - firstUnit.bounds.top;

        // Calculate total height of all elements
        const totalHeight = units.reduce((sum, unit) => sum + unit.bounds.height, 0);

        // Calculate available space for gaps
        const availableSpace = totalSpace - totalHeight;
        const gap = availableSpace / (units.length - 1);

        // Distribute all elements with equal gaps
        let currentY = firstUnit.bounds.top + firstUnit.bounds.height;

        for (let i = 1; i < units.length; i++) {
            currentY += gap;
            const unit = units[i];
            const deltaY = currentY - unit.bounds.top;

            if (unit.isGrid) {
                unit.element.y_position += deltaY;
            } else {
                unit.element.points.forEach(point => {
                    point.y += deltaY;
                });
            }

            currentY += unit.bounds.height;
        }
    }
}

// Distribute mixed elements with no spacing (edges touching)
function distributeMixedElementsNoSpacing(elements, direction) {
    if (elements.length < 2) return;

    // Create units with bounds for all elements (both DOM and canvas)
    const units = elements.map(element => {
        let bounds;

        if (element.x_position !== undefined) {
            // Grid element (DOM)
            bounds = {
                left: element.x_position,
                top: element.y_position,
                right: element.x_position + element.width,
                bottom: element.y_position + element.height,
                width: element.width,
                height: element.height
            };
        } else {
            // Canvas drawing - use VISUAL bounds (no padding) for edge-to-edge positioning
            const visualBounds = getDrawingVisualBounds(element);
            bounds = {
                left: visualBounds.left,
                top: visualBounds.top,
                right: visualBounds.right,
                bottom: visualBounds.bottom,
                width: visualBounds.width,
                height: visualBounds.height
            };
        }

        return {
            element: element,
            bounds: bounds,
            isGrid: element.x_position !== undefined
        };
    });

    if (direction === 'horizontal-no-spacing') {
        // Sort by left position
        units.sort((a, b) => a.bounds.left - b.bounds.left);

        // First element stays in place - start stacking from its right edge
        let currentX = units[0].bounds.right;

        for (let i = 1; i < units.length; i++) {
            const unit = units[i];

            // Calculate how much to move this element so its LEFT edge is at currentX
            const deltaX = currentX - unit.bounds.left;

            if (unit.isGrid) {
                // Move grid element
                unit.element.x_position += deltaX;
            } else {
                // Move canvas drawing - move all points
                unit.element.points.forEach(point => {
                    point.x += deltaX;
                });
            }

            // Update currentX to where the next element should start (right edge of this element)
            currentX = currentX + unit.bounds.width;
        }
    } else if (direction === 'vertical-no-spacing') {
        // Sort by top position
        units.sort((a, b) => a.bounds.top - b.bounds.top);

        // Start from the topmost element and stack downward with no gaps
        let currentY = units[0].bounds.bottom;

        for (let i = 1; i < units.length; i++) {
            const unit = units[i];
            const deltaY = currentY - unit.bounds.top;

            if (unit.isGrid) {
                // Move grid element
                unit.element.y_position += deltaY;
            } else {
                // Move canvas drawing
                unit.element.points.forEach(point => {
                    point.y += deltaY;
                });
            }

            // Update currentY to the bottom edge of this element (after moving)
            currentY = currentY + unit.bounds.height;
        }
    }
}

// Grouping functions
function createGroup() {
    const elements = getSelectedElements();
    if (elements.length < 2) {
        alert('Please select at least 2 elements to create a group');
        return;
    }

    closeAllDropdowns();

    const groupName = prompt('Enter group name:', `Group ${groupCounter + 1}`);
    if (!groupName) return;

    const group = {
        id: ++groupCounter,
        name: groupName,
        elements: elements.map(el => ({
            id: el.id || el.drawingId,
            type: el.x_position !== undefined ? 'grid' : 'canvas',
            element: el
        })),
        created: new Date().toISOString()
    };

    elementGroups.push(group);
    updateGroupManager();
    saveToHistory(`Create group: ${groupName}`);

    alert(`Created group "${groupName}" with ${elements.length} elements`);
}

function ungroupElements() {
    const elements = getSelectedElements();
    if (elements.length === 0) {
        alert('Please select elements to ungroup');
        return;
    }

    closeAllDropdowns();

    let ungroupedCount = 0;
    elements.forEach(element => {
        const elementId = element.id || element.drawingId;

        // Find and remove from groups
        elementGroups.forEach((group, groupIndex) => {
            const elementIndex = group.elements.findIndex(el => el.id === elementId);
            if (elementIndex !== -1) {
                group.elements.splice(elementIndex, 1);
                ungroupedCount++;

                // Remove group if empty
                if (group.elements.length === 0) {
                    elementGroups.splice(groupIndex, 1);
                }
            }
        });
    });

    if (ungroupedCount > 0) {
        updateGroupManager();
        saveToHistory(`Ungroup ${ungroupedCount} elements`);
        alert(`Ungrouped ${ungroupedCount} elements`);
    } else {
        alert('Selected elements are not in any groups');
    }
}

function toggleGroupManager() {
    const panel = document.getElementById('group-manager');
    const isVisible = panel.style.display !== 'none';

    if (isVisible) {
        panel.style.display = 'none';
    } else {
        panel.style.display = 'block';
        updateGroupManager();
    }

    closeAllDropdowns();
}

function updateGroupManager() {
    const groupsList = document.getElementById('groups-list');

    if (elementGroups.length === 0) {
        groupsList.innerHTML = `
            <div style="text-align: center; color: #999; padding: 20px 10px; font-size: 11px;">
                <i class="fas fa-object-group" style="font-size: 24px; margin-bottom: 8px; opacity: 0.5;"></i>
                <div>No groups created</div>
                <div style="font-size: 10px; margin-top: 4px;">Select elements and create a group</div>
            </div>
        `;
        return;
    }

    let html = '';
    elementGroups.forEach(group => {
        const validElements = group.elements.filter(el => el.element);
        const gridCount = validElements.filter(el => el.type === 'grid').length;
        const canvasCount = validElements.filter(el => el.type === 'canvas').length;

        // Determine group type icon
        let groupIcon = 'fas fa-object-group';
        if (gridCount > 0 && canvasCount > 0) {
            groupIcon = 'fas fa-layer-group'; // Mixed group
        } else if (canvasCount > 0) {
            groupIcon = 'fas fa-draw-polygon'; // Canvas only
        } else {
            groupIcon = 'fas fa-th'; // Grid only
        }

        html += `
            <div class="group-item" data-group-id="${group.id}">
                <div class="group-header" onclick="toggleGroupExpansion(${group.id})">
                    <div class="group-toggle">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                    <div class="group-icon">
                        <i class="${groupIcon}"></i>
                    </div>
                    <div class="group-name">${group.name}</div>
                    <div class="group-actions" onclick="event.stopPropagation()">
                        <button onclick="selectGroup(${group.id})" title="Select all">
                            <i class="fas fa-mouse-pointer"></i>
                        </button>
                        <button onclick="renameGroup(${group.id})" title="Rename">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="ungroupSpecificGroup(${group.id})" title="Ungroup">
                            <i class="fas fa-object-ungroup"></i>
                        </button>
                        <button onclick="deleteGroup(${group.id})" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="group-elements">
                    ${validElements.map((el, index) => `
                        <div class="group-element-item" data-group-id="${group.id}" data-element-index="${index}" data-element-id="${el.element.id}">
                            <div class="group-element-icon">
                                <i class="fas fa-${el.type === 'grid' ? 'square' : 'draw-polygon'}"></i>
                            </div>
                            <div class="group-element-name">${el.element.name || el.element.tool || 'Element'}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    });

    groupsList.innerHTML = html;

    // Add event delegation for group element clicks
    setupGroupElementClickHandlers();
}

function setupGroupElementClickHandlers() {
    const groupsList = document.getElementById('groups-list');

    // Remove any existing event listeners
    groupsList.removeEventListener('click', handleGroupElementClick);

    // Add event delegation for group element clicks
    groupsList.addEventListener('click', handleGroupElementClick);
}

function handleGroupElementClick(event) {
    const groupElementItem = event.target.closest('.group-element-item');
    if (!groupElementItem) return;

    event.stopPropagation();

    const groupId = groupElementItem.dataset.groupId;
    const elementIndex = groupElementItem.dataset.elementIndex;

    if (groupId && elementIndex !== undefined) {
        selectSingleElementFromGroup(groupId, parseInt(elementIndex));
    }
}

function toggleGroupExpansion(groupId) {
    const groupItem = document.querySelector(`[data-group-id="${groupId}"]`);
    const toggle = groupItem.querySelector('.group-toggle i');

    if (groupItem.classList.contains('expanded')) {
        groupItem.classList.remove('expanded');
        toggle.className = 'fas fa-chevron-right';
    } else {
        groupItem.classList.add('expanded');
        toggle.className = 'fas fa-chevron-down';
    }
}

function highlightGroupInManager(groupId) {
    // Clear previous highlights
    document.querySelectorAll('.group-item').forEach(item => {
        item.classList.remove('selected');
    });

    // Highlight the specific group
    const groupItem = document.querySelector(`[data-group-id="${groupId}"]`);
    if (groupItem) {
        groupItem.classList.add('selected');

        // Scroll into view if needed
        groupItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }
}

function findGroupContainingElement(element) {
    // Find which group contains this element using ID comparison
    const elementId = element.id || element.drawingId;
    const isDrawing = element.points !== undefined;

    for (let group of elementGroups) {
        const found = group.elements.find(el =>
            el.id === elementId &&
            ((el.type === 'canvas') === isDrawing)
        );
        if (found) {
            return group.id;
        }
    }
    return null;
}

function handleGroupClick(element, isDoubleClick = false) {
    const groupId = findGroupContainingElement(element);
    if (!groupId) return false;

    if (isDoubleClick) {
        // Double click: Open group manager and highlight group
        const groupManager = document.getElementById('group-manager');
        if (groupManager.style.display === 'none') {
            toggleGroupManager();
        }
        setTimeout(() => highlightGroupInManager(groupId), 100);
    } else {
        // Single click: Highlight group if manager is open
        const groupManager = document.getElementById('group-manager');
        if (groupManager.style.display !== 'none') {
            highlightGroupInManager(groupId);
        }
    }

    return true; // Indicates this element is part of a group
}

function selectGroup(groupId) {
    const group = elementGroups.find(g => g.id === groupId);
    if (!group) return;

    // Clear current selections
    clearSelection();
    removeAllResizeHandles();
    removeDrawingHandles();

    // Select all elements in the group
    group.elements.forEach(groupElement => {
        if (groupElement && groupElement.element) {
            if (groupElement.type === 'grid') {
                // Add grid element to multi-selection
                if (!selectedElements.includes(groupElement.element)) {
                    selectedElements.push(groupElement.element);
                }
            } else if (groupElement.type === 'canvas') {
                // Add canvas drawing to multi-selection
                if (!selectedDrawings.includes(groupElement.element)) {
                    selectedDrawings.push(groupElement.element);
                }
            }
        }
    });

    // Update visual display
    updateSelectionDisplay();

    // Highlight group in manager
    document.querySelectorAll('.group-item').forEach(item => {
        item.classList.remove('selected');
    });
    document.querySelector(`[data-group-id="${groupId}"]`).classList.add('selected');
}

function renameGroup(groupId) {
    const group = elementGroups.find(g => g.id === groupId);
    if (!group) return;

    const newName = prompt(`Rename group "${group.name}":`, group.name);
    if (newName && newName.trim() !== '' && newName !== group.name) {
        const oldName = group.name;
        group.name = newName.trim();
        updateGroupManager();
        saveToHistory(`Rename group from "${oldName}" to "${group.name}"`);
    }
}

function ungroupSpecificGroup(groupId) {
    const group = elementGroups.find(g => g.id === groupId);
    if (!group) return;

    if (confirm(`Ungroup "${group.name}"? This will remove the group but keep all elements.`)) {
        const index = elementGroups.findIndex(g => g.id === groupId);
        elementGroups.splice(index, 1);
        updateGroupManager();
        saveToHistory(`Ungroup: ${group.name}`);
    }
}

function deleteGroup(groupId) {
    const group = elementGroups.find(g => g.id === groupId);
    if (!group) return;

    if (confirm(`Delete group "${group.name}"? This will not delete the elements, only the group.`)) {
        const index = elementGroups.findIndex(g => g.id === groupId);
        elementGroups.splice(index, 1);
        updateGroupManager();
        saveToHistory(`Delete group: ${group.name}`);
    }
}

// Track which element was selected from group manager for updating group data
let selectedFromGroupManager = null;

function selectSingleElementFromGroup(groupId, elementIndex) {
    // Convert groupId to number to match group.id type
    const numericGroupId = parseInt(groupId);
    const group = elementGroups.find(g => g.id === numericGroupId);
    if (!group || !group.elements[elementIndex]) {
        return;
    }

    const groupElement = group.elements[elementIndex];
    const element = groupElement.element;

    // If element reference is broken, try to reconnect it
    if (!element) {
        if (groupElement.type === 'grid') {
            const reconnectedElement = layoutElements.find(el => el.id === groupElement.id);
            if (reconnectedElement) {
                groupElement.element = reconnectedElement;
            } else {
                return;
            }
        } else if (groupElement.type === 'canvas') {
            const reconnectedDrawing = drawings.find(drawing => drawing.id === groupElement.id);
            if (reconnectedDrawing) {
                groupElement.element = reconnectedDrawing;
            } else {
                return;
            }
        }
    }

    // Clear all current selections
    clearSelection();
    removeDrawingHandles();
    removeAllResizeHandles();

    // Get the current element reference (might have been reconnected)
    const currentElement = groupElement.element;

    // Track that this element was selected from group manager
    selectedFromGroupManager = {
        groupId: numericGroupId,
        elementIndex: elementIndex,
        originalData: JSON.parse(JSON.stringify(currentElement)) // Store original state
    };

    // Select the element based on its type
    if (groupElement.type === 'grid') {
        // Grid element selection
        selectedElement = currentElement;
        const elementDiv = document.getElementById('element_' + currentElement.id);
        if (elementDiv) {
            addResizeHandles(elementDiv, currentElement);
        }
        showElementProperties(currentElement);
    } else if (groupElement.type === 'canvas') {
        // Canvas drawing selection
        selectedDrawing = currentElement;
        addDrawingHandles(currentElement);
        if (currentElement.tool === 'road') {
            showRoadEditor(currentElement);
        } else {
            showGeneralDrawingEditor(currentElement);
        }
    }

    // Update visual selection in group manager
    updateGroupManagerSelection(numericGroupId, elementIndex);
    updateSelectionDisplay();
}

function updateGroupManagerSelection(groupId, elementIndex) {
    // Clear all previous selections in group manager
    document.querySelectorAll('.group-element-item').forEach(item => {
        item.classList.remove('selected');
    });

    // Highlight the selected element
    const groupItem = document.querySelector(`[data-group-id="${groupId}"]`);
    if (groupItem) {
        const elementItems = groupItem.querySelectorAll('.group-element-item');
        if (elementItems[elementIndex]) {
            elementItems[elementIndex].classList.add('selected');
        }
    }
}

function updateGroupDataAfterModification() {
    if (!selectedFromGroupManager) return;

    const { groupId, elementIndex, originalData } = selectedFromGroupManager;
    const group = elementGroups.find(g => g.id === groupId);
    if (!group || !group.elements[elementIndex]) return;

    const groupElement = group.elements[elementIndex];
    const currentElement = groupElement.element;

    // Check if element has been modified
    const hasChanged = JSON.stringify(currentElement) !== JSON.stringify(originalData);

    if (hasChanged) {
        // Update the group element reference with current data
        groupElement.element = currentElement;

        // Save to history
        saveToHistory(`Update element in group: ${group.name}`);
    }

    // Clear the tracking
    selectedFromGroupManager = null;
}

// Draggable Properties Panel
let isDraggingProperties = false;
let propertiesDragOffset = { x: 0, y: 0 };

function setupDraggablePropertiesPanel() {
    const propertiesPanel = document.getElementById('element-properties');
    const propertiesHeader = document.getElementById('properties-header');

    if (!propertiesPanel || !propertiesHeader) return;

    propertiesHeader.addEventListener('mousedown', startPropertiesDrag);
}

function startPropertiesDrag(e) {
    if (e.target.closest('.element-properties-close')) return; // Don't drag when clicking close button

    isDraggingProperties = true;
    const propertiesPanel = document.getElementById('element-properties');
    const rect = propertiesPanel.getBoundingClientRect();

    propertiesDragOffset.x = e.clientX - rect.left;
    propertiesDragOffset.y = e.clientY - rect.top;

    document.addEventListener('mousemove', dragProperties);
    document.addEventListener('mouseup', stopPropertiesDrag);

    e.preventDefault();
}

function dragProperties(e) {
    if (!isDraggingProperties) return;

    const propertiesPanel = document.getElementById('element-properties');
    const newX = e.clientX - propertiesDragOffset.x;
    const newY = e.clientY - propertiesDragOffset.y;

    // Keep panel within viewport bounds
    const maxX = window.innerWidth - propertiesPanel.offsetWidth;
    const maxY = window.innerHeight - propertiesPanel.offsetHeight;

    const clampedX = Math.max(0, Math.min(newX, maxX));
    const clampedY = Math.max(0, Math.min(newY, maxY));

    propertiesPanel.style.left = clampedX + 'px';
    propertiesPanel.style.top = clampedY + 'px';
    propertiesPanel.style.transform = 'none'; // Remove centering transform
}

function stopPropertiesDrag() {
    isDraggingProperties = false;
    document.removeEventListener('mousemove', dragProperties);
    document.removeEventListener('mouseup', stopPropertiesDrag);
}

function closeElementProperties() {
    const propertiesPanel = document.getElementById('element-properties');
    propertiesPanel.style.display = 'none';

    // Clear selection when closing properties
    clearSelection();
    removeAllResizeHandles();
}

function initializeDesigner() {
    const canvas = document.getElementById('design-canvas');

    // Canvas click to deselect
    canvas.addEventListener('click', function(e) {
        if (e.target === canvas) {
            // Check if we clicked on empty space (not on an element or drawing)
            const rect = canvas.getBoundingClientRect();
            const clickPoint = {
                x: (e.clientX - rect.left) / zoomLevel,
                y: (e.clientY - rect.top) / zoomLevel
            };

            // Check if click is on any drawing
            let clickedOnDrawing = false;
            for (let i = drawings.length - 1; i >= 0; i--) {
                if (isPointOnDrawing(clickPoint, drawings[i])) {
                    clickedOnDrawing = true;
                    break;
                }
            }

            // Check if click is on any grid element
            let clickedOnElement = false;
            for (let element of layoutElements) {
                if (clickPoint.x >= element.x_position &&
                    clickPoint.x <= element.x_position + element.width &&
                    clickPoint.y >= element.y_position &&
                    clickPoint.y <= element.y_position + element.height) {
                    clickedOnElement = true;
                    break;
                }
            }

            // Only clear selections if clicking on truly empty space
            if (!clickedOnDrawing && !clickedOnElement) {
                clearSelection();
                removeAllResizeHandles();
                removeDrawingHandles();
                hideRoadEditor();
                hideGeneralDrawingEditor();
                document.getElementById('element-properties').style.display = 'none';
            }
        }
    });

    // Prevent context menu
    canvas.addEventListener('contextmenu', function(e) {
        e.preventDefault();
    });

    // Initialize category badge drag and drop
    initializeCategoryDragDrop();
}

function initializeCategoryDragDrop() {
    const canvas = document.getElementById('design-canvas');
    const categoryBadges = document.querySelectorAll('.category-badge');

    // Add drag event listeners to category badges
    categoryBadges.forEach(badge => {
        badge.addEventListener('dragstart', function(e) {
            const categoryId = this.getAttribute('data-category-id');
            const categoryName = this.getAttribute('data-category-name');

            e.dataTransfer.setData('text/plain', JSON.stringify({
                type: 'category',
                categoryId: categoryId,
                categoryName: categoryName
            }));

            e.dataTransfer.effectAllowed = 'copy';
        });
    });

    // Add drop event listeners to canvas
    canvas.addEventListener('dragover', function(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'copy';
        this.classList.add('drag-over');
    });

    canvas.addEventListener('dragleave', function(e) {
        if (!this.contains(e.relatedTarget)) {
            this.classList.remove('drag-over');
        }
    });

    canvas.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('drag-over');

        try {
            const data = JSON.parse(e.dataTransfer.getData('text/plain'));

            if (data.type === 'category') {
                // Get drop position relative to canvas
                const rect = this.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                // Create show parking element with category assignment
                createShowParkingFromCategory(data.categoryId, data.categoryName, x, y);
            }
        } catch (error) {

        }
    });
}

function createShowParkingFromCategory(categoryId, categoryName, x, y) {
    // Generate unique ID for new element
    const newId = 'new_' + (++elementCounter);

    // Get category prefix for consistent naming
    const categoryPrefix = getCategoryPrefix(categoryName);

    const element = {
        id: newId,
        layout_id: layoutId,
        element_type: 'show_parking',
        name: categoryName + ' Parking',
        x_position: Math.max(0, x - 150), // Center the element on drop point
        y_position: Math.max(0, y - 100),
        width: 300,
        height: 200,
        rotation: 0,
        color: '#007bff',
        border_color: '#007bff',
        opacity: 1.0,
        z_index: 1,
        properties: {
            category: categoryId,
            vehicle_capacity: 50
        },
        category_id: parseInt(categoryId),
        capacity: 50,
        handicap_accessible: 0
    };

    layoutElements.push(element);
    saveToHistory(`Add ${categoryName} Parking from category drag`);
    renderElements();
    updateStats();
    updateElementList();
    selectElement(element);

    // Show success message with category prefix
    showToast(`Created ${categoryName} Parking section <${categoryPrefix}>`, 'success');
}

function showToast(message, type = 'info') {
    // Simple toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 250px;';
    toast.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-check-circle me-2"></i>
            ${message}
        </div>
    `;

    document.body.appendChild(toast);

    // Auto remove after 3 seconds
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

/**
 * Generate category prefix using the same logic as RegistrationModel
 * This ensures consistency across the entire site
 */
function getCategoryPrefix(categoryName) {
    if (!categoryName) {
        return 'X'; // Default prefix if category not found
    }

    // Clean the category name - remove parentheses and special characters
    let cleanName = categoryName.replace(/\([^)]*\)/g, ''); // Remove content in parentheses
    cleanName = cleanName.replace(/[^a-zA-Z0-9\s]/g, '').trim(); // Remove special characters

    // Get words from the cleaned name
    let words = cleanName.split(' ').filter(word => word.length > 0);
    let prefix = '';

    // Handle different category naming patterns
    if (words.length === 0) {
        // If no valid words after cleaning, use first two letters of original name
        const cleanOriginal = categoryName.replace(/[^a-zA-Z0-9]/g, '');
        prefix = cleanOriginal.substring(0, 2).toUpperCase();
    } else if (words.length === 1) {
        // For single words, use first two letters
        prefix = words[0].substring(0, 2).toUpperCase();
    } else if (words.length === 2) {
        // For two words, use first letter of each
        prefix = (words[0].charAt(0) + words[1].charAt(0)).toUpperCase();
    } else {
        // For multiple words, use a more distinctive approach
        if (words[0].length <= 3) {
            // If first word is short (like "The", "A", etc.), use first letter of first word and first letter of second word
            prefix = (words[0].charAt(0) + words[1].charAt(0)).toUpperCase();
        } else {
            // Otherwise use first letter of first word and first letter of last significant word
            prefix = (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
        }
    }

    // Special case handling for common categories (same as RegistrationModel)
    const lowerName = categoryName.toLowerCase();
    if (lowerName.includes('classic')) {
        prefix = 'CL';
    } else if (lowerName.includes('muscle')) {
        prefix = 'MU';
    } else if (lowerName.includes('truck') || lowerName.includes('suv')) {
        prefix = 'TR';
    } else if (lowerName.includes('motorcycle') || lowerName.includes('bike')) {
        prefix = 'MC';
    } else if (lowerName.includes('import') && lowerName.includes('asian')) {
        prefix = 'AI';
    } else if (lowerName.includes('import') && lowerName.includes('european')) {
        prefix = 'EI';
    } else if (lowerName.includes('modern')) {
        prefix = 'MO';
    } else if (lowerName.includes('custom') || lowerName.includes('modified')) {
        prefix = 'CM';
    } else if (lowerName.includes('contemporary')) {
        prefix = 'CT';
    } else if (lowerName.includes('antique')) {
        prefix = 'AN';
    } else if (lowerName.includes('vintage')) {
        prefix = 'VN';
    } else if (lowerName.includes('sport')) {
        prefix = 'SP';
    } else if (lowerName.includes('luxury')) {
        prefix = 'LX';
    } else if (lowerName.includes('electric') || lowerName.includes('ev')) {
        prefix = 'EV';
    } else if (lowerName.includes('hybrid')) {
        prefix = 'HY';
    } else if (lowerName.includes('convertible')) {
        prefix = 'CV';
    } else if (lowerName.includes('coupe')) {
        prefix = 'CP';
    } else if (lowerName.includes('sedan')) {
        prefix = 'SD';
    } else if (lowerName.includes('race') || lowerName.includes('racing')) {
        prefix = 'RC';
    } else if (lowerName.includes('off') && lowerName.includes('road')) {
        prefix = 'OR';
    }

    // For custom categories, ensure we have a meaningful 2-letter prefix
    if (prefix.length !== 2) {
        if (prefix.length === 1) {
            // Try to find a second significant letter
            if (words.length > 1) {
                // Use the first letter of the second word
                prefix += words[1].charAt(0).toUpperCase();
            } else if (words[0].length > 1) {
                // Use the second letter of the first word
                prefix += words[0].charAt(1).toUpperCase();
            } else {
                // Fallback: add X
                prefix += 'X';
            }
        } else if (prefix.length > 2) {
            // If we have more than 2 letters, truncate to 2
            prefix = prefix.substring(0, 2);
        } else {
            // If we somehow have no prefix, use the first two letters of the category name
            const categoryName_clean = categoryName.replace(/[^a-zA-Z0-9]/g, '');
            if (categoryName_clean.length > 1) {
                prefix = categoryName_clean.substring(0, 2).toUpperCase();
            } else if (categoryName_clean.length === 1) {
                prefix = categoryName_clean.toUpperCase() + 'X';
            } else {
                prefix = 'XX'; // Last resort fallback
            }
        }
    }

    return prefix;
}

// ===== ROAD EDITING FUNCTIONS =====

let selectedRoad = null;

// selectedDrawing is already declared in the global canvas drawing system variables above

function selectDrawingAtPoint(clickPoint, event = null) {
    // Check if Ctrl key is being held for multi-selection
    const isCtrlHeld = event && (event.ctrlKey || event.metaKey);
    const shouldMultiSelect = isCtrlHeld;

    // Find any drawing at click point
    for (let i = drawings.length - 1; i >= 0; i--) {
        const drawing = drawings[i];
        if (isPointOnDrawing(clickPoint, drawing)) {
            if (shouldMultiSelect) {
                // Multi-selection mode for drawings
                // Check if this drawing is part of a group
                const group = findElementGroup(drawing);
                if (group) {
                    selectEntireGroupMulti(group);

                    // Handle group highlighting in group manager
                    const isDoubleClick = event && event.detail === 2;
                    handleGroupClick(drawing, isDoubleClick);

                    updateSelectionDisplay();
                    return;
                }

                // If this is the first Ctrl+click and we have a single drawing selection, move to multi-selection mode
                if (selectedDrawing && selectedDrawings.length === 0) {
                    // Move the currently selected drawing to multi-selection array
                    selectedDrawings.push(selectedDrawing);
                    selectedDrawing = null; // Clear single selection
                    removeDrawingHandles(); // Remove single selection handles
                }

                // If we have grid elements selected, we're creating a mixed selection
                // Clear any single grid element selection and move it to multi-selection
                if (selectedElement && selectedElements.length === 0) {
                    selectedElements.push(selectedElement);
                    selectedElement = null; // Clear single selection
                    removeAllResizeHandles(); // Remove single selection handles
                }

                if (isElementSelected(drawing, true)) {
                    // Drawing is already selected
                    // Only remove it if there are multiple items selected
                    // If it's the only selected item, keep it selected (like grid elements)
                    const totalSelected = selectedDrawings.length + (selectedDrawing ? 1 : 0);
                    if (totalSelected > 1) {
                        removeFromSelection(drawing, true);
                    }
                    // If only one item selected, do nothing (keep it selected)
                } else {
                    // Add to selection
                    addToSelection(drawing, true);
                }

                updateSelectionDisplay();
                return;
            } else {
                // Single selection mode
                // If already selected, don't start drag here (mousedown handler will do that)
                if (selectedDrawing && selectedDrawing.id === drawing.id) {
                    return; // Already selected, no need to reselect
                }

                // Check if this drawing is part of a group
                const group = findElementGroup(drawing);
                if (group) {
                    selectEntireGroup(group);

                    // Handle group highlighting in group manager
                    const isDoubleClick = event && event.detail === 2;
                    handleGroupClick(drawing, isDoubleClick);

                    updateSelectionDisplay();
                    return;
                }

                // Clear all selections and select this drawing
                clearSelection();
                removeDrawingHandles();

                selectedDrawing = drawing;
                selectedRoad = drawing.tool === 'road' ? drawing : null; // Keep for compatibility

                // Close any existing editors first
                hideRoadEditor();
                hideGeneralDrawingEditor();

                // Force immediate redraw to show selection BEFORE showing editor
                redrawCanvas();

                // Add manipulation handles

                // Ensure handle container exists before using it
                if (!drawingHandleContainer) {
                    const canvas = document.getElementById('drawing-canvas');
                    const canvasContainer = canvas.parentElement;
                    drawingHandleContainer = document.createElement('div');
                    drawingHandleContainer.id = 'drawing-handles';
                    drawingHandleContainer.style.cssText = `
                        position: absolute;
                        top: 0;
                        left: 0;
                        pointer-events: none;
                        z-index: 1000;
                        width: 100%;
                        height: 100%;
                    `;
                    canvasContainer.appendChild(drawingHandleContainer);
                }

                addDrawingHandles(drawing);

                // Show size indicator for selected drawing
                if (!['road', 'text', 'freehand'].includes(drawing.tool)) {
                    const bounds = getDrawingBounds(drawing);
                    showSizeIndicator(drawing, bounds, drawing.tool);
                }

                if (drawing.tool === 'road') {
                    showRoadEditor(drawing);
                } else {
                    showGeneralDrawingEditor(drawing);
                }

                return;
            }
        }
    }

    // No drawing found, hide editors
    selectedDrawing = null;
    selectedRoad = null;
    hideRoadEditor();
    hideGeneralDrawingEditor();
    removeDrawingHandles();
    redrawCanvas(); // Force redraw to remove selection
}

function isPointOnRoad(point, road) {
    if (road.points.length < 2) return false;

    const start = road.points[0];
    const end = road.points[1];
    const roadWidth = road.style.width;

    // Calculate distance from point to line segment
    const A = point.x - start.x;
    const B = point.y - start.y;
    const C = end.x - start.x;
    const D = end.y - start.y;

    const dot = A * C + B * D;
    const lenSq = C * C + D * D;

    if (lenSq === 0) return false;

    const param = dot / lenSq;

    let xx, yy;
    if (param < 0) {
        xx = start.x;
        yy = start.y;
    } else if (param > 1) {
        xx = end.x;
        yy = end.y;
    } else {
        xx = start.x + param * C;
        yy = start.y + param * D;
    }

    const dx = point.x - xx;
    const dy = point.y - yy;
    const distance = Math.sqrt(dx * dx + dy * dy);

    return distance <= roadWidth / 2;
}

function showRoadEditor(road) {
    // Remove existing editor
    hideRoadEditor();

    // Create road editor panel
    const editor = document.createElement('div');
    editor.id = 'road-editor';
    editor.className = 'position-fixed bg-white border rounded shadow p-3';
    editor.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 250px;';
    editor.dataset.roadId = road.id; // Store road ID

    editor.innerHTML = `
        <h6><i class="fas fa-road"></i> Road Editor</h6>
        <div class="mb-2">
            <label class="form-label small">Road Name</label>
            <input type="text" class="form-control form-control-sm" id="road-name-input"
                   value="${road.name || ''}" placeholder="Enter road name"
                   onchange="updateRoadName(this.value)" onkeyup="updateRoadName(this.value)">
        </div>
        <div class="mb-2">
            <label class="form-label small">Text Position</label>
            <select class="form-select form-select-sm" onchange="updateRoadNamePosition(this.value)">
                <option value="center" ${(road.namePosition || 'center') === 'center' ? 'selected' : ''}>Center</option>
                <option value="left" ${(road.namePosition || 'center') === 'left' ? 'selected' : ''}>Left</option>
                <option value="right" ${(road.namePosition || 'center') === 'right' ? 'selected' : ''}>Right</option>
            </select>
        </div>
        <div class="mb-2">
            <label class="form-label small">Width</label>
            <input type="range" class="form-range" id="road-width-slider"
                   min="0" max="2" step="1" value="${road.style.width === 20 ? 0 : road.style.width === 40 ? 1 : 2}"
                   onchange="updateRoadWidth(this.value, true)" oninput="updateRoadWidth(this.value, false)">
            <small class="text-muted"><span id="road-width-display">${road.style.width}</span>px (20/40/60)</small>
        </div>
        <div class="mb-2">
            <label class="form-label small">Road Color</label>
            <input type="color" class="form-control form-control-sm"
                   value="${road.style.color}" onchange="updateRoadColor(this.value)">
        </div>
        <div class="mb-2">
            <label class="form-label small">Text Color</label>
            <input type="color" class="form-control form-control-sm"
                   value="${road.textColor || '#ffffff'}" onchange="updateRoadTextColor(this.value)">
        </div>
        <div class="d-grid gap-2">
            <button class="btn btn-danger btn-sm" onclick="deleteSelectedRoad()">
                <i class="fas fa-trash"></i> Delete Road
            </button>
            <button class="btn btn-secondary btn-sm" onclick="hideRoadEditor()">
                Close
            </button>
        </div>
    `;

    document.body.appendChild(editor);
}

function showGeneralDrawingEditor(drawing) {


    // Remove existing editor
    hideGeneralDrawingEditor();

    // Create general drawing editor panel
    const editor = document.createElement('div');
    editor.id = 'drawing-editor';
    editor.className = 'position-fixed bg-white border rounded shadow p-3';
    editor.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 250px;';
    editor.dataset.drawingId = drawing.id; // Store drawing ID

    const toolName = drawing.tool.charAt(0).toUpperCase() + drawing.tool.slice(1);


    // Determine slider mode
    const isText = drawing.tool === 'text';
    const isFilled = drawing.style.fill;
    const isShapeType = drawing.tool === 'rectangle' || drawing.tool === 'circle' || drawing.tool === 'island';
    const showSize = !isText && isFilled && isShapeType;

    // Determine current value for the slider
    let currentValue = isText ? (drawing.style.fontSize || 16) : drawing.style.width;
    if (showSize && drawing.points.length >= 2) {
        if (drawing.tool === 'circle') {
            const radius = Math.sqrt(
                Math.pow(drawing.points[1].x - drawing.points[0].x, 2) +
                Math.pow(drawing.points[1].y - drawing.points[0].y, 2)
            );
            currentValue = radius * 2;
        } else {
            const width = Math.abs(drawing.points[1].x - drawing.points[0].x);
            currentValue = width;
        }
    }


    editor.innerHTML = `
        <h6><i class="fas fa-${getToolIcon(drawing.tool)}"></i> ${toolName} Editor</h6>
        <div class="mb-2">
            <label class="form-label small">Color</label>
            <input type="color" class="form-control form-control-sm"
                   value="${drawing.style.color}" onchange="updateSelectedDrawingColor(this.value)">
        </div>
        <div class="mb-2">
            <label class="form-label small" id="drawing-width-label">${isText ? 'Font Size' : (showSize ? 'Size' : 'Width')}</label>
            <input type="range" class="form-range" id="drawing-editor-width-slider"
                   min="${isText ? '8' : (showSize ? '10' : '1')}" max="${isText ? '72' : (showSize ? '200' : '50')}" value="${Math.round(currentValue)}"
                   onchange="updateSelectedDrawingWidth(this.value, true)" oninput="updateSelectedDrawingWidth(this.value, false)">
            <small class="text-muted"><span id="drawing-editor-width-display">${Math.round(currentValue)}</span>px</small>
        </div>
        ${drawing.tool === 'rectangle' || drawing.tool === 'circle' || drawing.tool === 'island' ? `
        <div class="mb-2">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="drawing-editor-fill-check"
                       ${drawing.style.fill ? 'checked' : ''} onchange="updateSelectedDrawingFill(this.checked); updateWidthLabel()">
                <label class="form-check-label small" for="drawing-editor-fill-check">
                    Fill Shape
                </label>
            </div>
        </div>
        ` : ''}
        <div class="d-grid gap-2">
            <button class="btn btn-danger btn-sm" onclick="deleteSelectedDrawing()">
                <i class="fas fa-trash"></i> Delete ${toolName}
            </button>
            <button class="btn btn-secondary btn-sm" onclick="hideGeneralDrawingEditor()">
                Close
            </button>
        </div>
    `;

    document.body.appendChild(editor);


    // Update the width label based on fill state
    updateWidthLabel();
}

function getToolIcon(tool) {
    switch (tool) {
        case 'line': return 'minus';
        case 'rectangle': return 'square';
        case 'circle': return 'circle';
        case 'freehand': return 'pencil-alt';
        case 'island': return 'leaf';
        case 'text': return 'font';
        default: return 'draw-polygon';
    }
}

function hideGeneralDrawingEditor() {
    const editor = document.getElementById('drawing-editor');
    if (editor) {
        editor.remove();
    }
}

function updateWidthLabel() {
    if (!selectedDrawing) return;

    const widthLabel = document.getElementById('drawing-width-label');
    const widthSlider = document.getElementById('drawing-editor-width-slider');
    const display = document.getElementById('drawing-editor-width-display');

    if (!widthLabel || !widthSlider) return;

    // Text: slider is font size
    if (selectedDrawing.tool === 'text') {
        widthLabel.textContent = 'Font Size';
        widthSlider.min = '8';
        widthSlider.max = '72';
        const fs = selectedDrawing.style.fontSize || 16;
        widthSlider.value = fs;
        if (display) display.textContent = fs;
        return;
    }

    const fillCheck = document.getElementById('drawing-editor-fill-check');
    if (fillCheck) {
        const isFilled = fillCheck.checked;
        if (isFilled && (selectedDrawing.tool === 'rectangle' || selectedDrawing.tool === 'circle' || selectedDrawing.tool === 'island')) {
            widthLabel.textContent = 'Size';
            widthSlider.min = '10';
            widthSlider.max = '200';
        } else {
            widthLabel.textContent = 'Width';
            widthSlider.min = '1';
            widthSlider.max = '50';
        }
    }
}

function updateShapeSize(drawing, newSize) {
    if (drawing.points.length < 2) return;

    const startPoint = drawing.points[0];
    const endPoint = drawing.points[1];

    switch (drawing.tool) {
        case 'rectangle':
        case 'island':
            // For rectangles, maintain aspect ratio and resize from center
            const currentWidth = Math.abs(endPoint.x - startPoint.x);
            const currentHeight = Math.abs(endPoint.y - startPoint.y);
            const aspectRatio = currentHeight / currentWidth;

            const newWidth = newSize;
            const newHeight = newSize * aspectRatio;

            // Calculate center point
            const drawingSizeCenterX = (startPoint.x + endPoint.x) / 2;
            const drawingSizeCenterY = (startPoint.y + endPoint.y) / 2;

            // Update points to maintain center
            drawing.points[0] = {
                x: drawingSizeCenterX - newWidth / 2,
                y: drawingSizeCenterY - newHeight / 2
            };
            drawing.points[1] = {
                x: drawingSizeCenterX + newWidth / 2,
                y: drawingSizeCenterY + newHeight / 2
            };
            break;

        case 'circle':
            // For circles, adjust radius
            const centerXCircle = startPoint.x;
            const centerYCircle = startPoint.y;
            const newRadius = newSize / 2;

            // Update end point to reflect new radius
            drawing.points[1] = {
                x: centerXCircle + newRadius,
                y: centerYCircle
            };
            break;

        case 'line':
        case 'road':
            // For lines and roads, scale the length while maintaining direction
            const currentLength = Math.sqrt(
                Math.pow(endPoint.x - startPoint.x, 2) +
                Math.pow(endPoint.y - startPoint.y, 2)
            );

            if (currentLength > 0) {
                const scale = newSize / currentLength;
                const deltaX = (endPoint.x - startPoint.x) * scale;
                const deltaY = (endPoint.y - startPoint.y) * scale;

                // Keep start point, scale end point
                drawing.points[1] = {
                    x: startPoint.x + deltaX,
                    y: startPoint.y + deltaY
                };
            }
            break;

        case 'freehand':
            // For freehand, scale all points relative to the center
            const bounds = getDrawingBounds(drawing);
            const drawingCenterX = bounds.centerX;
            const drawingCenterY = bounds.centerY;
            const currentSize = Math.max(bounds.width, bounds.height);

            if (currentSize > 0) {
                const scale = newSize / currentSize;

                drawing.points.forEach(point => {
                    const relativeX = point.x - drawingCenterX;
                    const relativeY = point.y - drawingCenterY;
                    point.x = drawingCenterX + relativeX * scale;
                    point.y = drawingCenterY + relativeY * scale;
                });
            }
            break;

        default:

            break;
    }

    // Store the size for future reference
    drawing.style.size = newSize;
}

// Drawing manipulation functions (drag, resize, rotate)
let isDraggingDrawing = false;
let isResizingDrawing = false;
let isRotatingDrawing = false;
let dragStartPoint = null;
let resizeHandle = null;
let rotationStartAngle = 0;
let lastKeyEvent = null; // Track last key event for angle snapping
// Match canvas rotation responsiveness to grid


function addDrawingHandles(drawing) {
    if (!drawing) {
        return;
    }

    // Text elements only need 1 point, other drawings need at least 2
    const minPoints = drawing.tool === 'text' ? 1 : 2;
    if (drawing.points.length < minPoints) {
        return;
    }

    // Remove existing handles first
    removeDrawingHandles();

    const canvas = document.getElementById('drawing-canvas');
    if (!canvas) {

        return;
    }
    const rect = canvas.getBoundingClientRect();


    // Use the proper getDrawingBounds function that works for all drawing types
    const drawingHandleBounds = getDrawingBounds(drawing);
    if (!drawingHandleBounds) {

        return;
    }



    // Create handle container as a child of the canvas container (like grid elements)
    const canvasContainer = canvas.parentElement;
    drawingHandleContainer = document.createElement('div');
    drawingHandleContainer.id = 'drawing-handles';
    drawingHandleContainer.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        pointer-events: none;
        z-index: 1000;
        width: 100%;
        height: 100%;
    `;

    // Get drawing rotation (default to 0 if not set)
    const drawingRotation = drawing.rotation || 0;
    // For text, use true rotation so handles rotate with the text box
    const rotationForHandles = drawingRotation;


    // Add resize handles - CANVAS DRAWING SYSTEM
    // Different handle sets based on drawing tool
    let drawingHandlePositions;
    switch (drawing.tool) {
        case 'line':
            // Lines only need east/west handles for length adjustment
            // Width is controlled by the width slider in toolbar
            drawingHandlePositions = ['e', 'w'];
            break;
        case 'road':
            // Roads need handles along their length direction (determined by road orientation)
            // Calculate road direction to determine which handles to show
            if (drawing.points && drawing.points.length >= 2) {
                const startPoint = drawing.points[0];
                const endPoint = drawing.points[1];
                const deltaX = Math.abs(endPoint.x - startPoint.x);
                const deltaY = Math.abs(endPoint.y - startPoint.y);

                // If road is more horizontal than vertical, use E/W handles
                // If road is more vertical than horizontal, use N/S handles
                if (deltaX >= deltaY) {
                    drawingHandlePositions = ['e', 'w']; // Horizontal road
                } else {
                    drawingHandlePositions = ['n', 's']; // Vertical road
                }
            } else {
                // Default to E/W if no points available
                drawingHandlePositions = ['e', 'w'];
            }

            break;
        case 'circle':
            // Circles need all handles for radius adjustment
            drawingHandlePositions = ['nw', 'ne', 'sw', 'se', 'n', 's', 'e', 'w'];

            break;
        case 'text':
            // Text needs all handles - corners for resizing, edges for moving
            drawingHandlePositions = ['nw', 'ne', 'sw', 'se', 'n', 's', 'e', 'w'];

            break;
        case 'rectangle':
        case 'island':
        case 'freehand':
        default:
            // Rectangles, islands, and freehand need all handles
            drawingHandlePositions = ['nw', 'ne', 'sw', 'se', 'n', 's', 'e', 'w'];

            break;
    }

    drawingHandlePositions.forEach(position => {
        const drawingHandle = createResizeHandle(position, drawingHandleBounds, rect, rotationForHandles);
        if (drawingHandle) {
            drawingHandleContainer.appendChild(drawingHandle);
        } else {

        }
    });

    // Add rotation handle - CANVAS DRAWING SYSTEM
    const drawingRotateHandle = createRotationHandle(drawingHandleBounds, rect, rotationForHandles);
    drawingHandleContainer.appendChild(drawingRotateHandle);

    // Add move cursor for the shape area
    addMoveCapability(drawing, drawingHandleBounds, rect);

    canvasContainer.appendChild(drawingHandleContainer);


    // Add drag functionality to the drawing itself
    // Create an invisible drag area over the entire drawing (different from grid dragArea)
    const drawingDragArea = document.createElement('div');
    drawingDragArea.className = 'drawing-drag-area';
    drawingDragArea.style.cssText = `
        position: absolute;
        left: ${drawingHandleBounds.x * zoomLevel}px;
        top: ${drawingHandleBounds.y * zoomLevel}px;
        width: ${drawingHandleBounds.width * zoomLevel}px;
        height: ${drawingHandleBounds.height * zoomLevel}px;
        cursor: move;
        z-index: 999;
        background: transparent;
        border: none;
        pointer-events: auto;
    `;

    // Add drag event listeners
    drawingDragArea.addEventListener('mousedown', (e) => {
        // If Ctrl/Meta is held, handle multi-select directly here (overlay would intercept the click)
        if (e.ctrlKey || e.metaKey) {
            const designCanvas = document.getElementById('design-canvas');
            const rect = designCanvas.getBoundingClientRect();
            const clickPoint = {
                x: (e.clientX - rect.left) / zoomLevel,
                y: (e.clientY - rect.top) / zoomLevel
            };
            selectDrawingAtPoint(clickPoint, e);
            return;
        }
        e.preventDefault();
        e.stopPropagation();

        startSingleDrawingDrag(e);
    });

    drawingHandleContainer.appendChild(drawingDragArea);





}

function getDrawingBounds(drawing) {

    if (!drawing || drawing.points.length < 1) return null;

    // Text only needs one point, other tools need at least 2
    if (drawing.tool !== 'text' && drawing.points.length < 2) return null;

    let minX, minY, maxX, maxY, centerX, centerY;

    // Handle different drawing types with specific bounds calculation
    switch (drawing.tool) {
        case 'circle':
            // For circles/ellipses: point[0] = center, point[1] = edge point (represents radii)
            const ellipseCenter = drawing.points[0];
            const ellipseEdge = drawing.points[1];

            // Calculate separate X and Y radii for ellipse support
            const radiusX = Math.abs(ellipseEdge.x - ellipseCenter.x);
            const radiusY = Math.abs(ellipseEdge.y - ellipseCenter.y);

            // Calculate bounds that encompass the entire ellipse
            minX = ellipseCenter.x - radiusX;
            minY = ellipseCenter.y - radiusY;
            maxX = ellipseCenter.x + radiusX;
            maxY = ellipseCenter.y + radiusY;
            centerX = ellipseCenter.x;
            centerY = ellipseCenter.y;


            break;

        default:
            // For all other drawing types: use min/max of all points
            minX = Infinity;
            minY = Infinity;
            maxX = -Infinity;
            maxY = -Infinity;

            drawing.points.forEach(point => {
                minX = Math.min(minX, point.x);
                minY = Math.min(minY, point.y);
                maxX = Math.max(maxX, point.x);
                maxY = Math.max(maxY, point.y);
            });

            centerX = (minX + maxX) / 2;
            centerY = (minY + maxY) / 2;
            break;

        case 'text':
            // For text, use unrotated logical bounds; rotation applied when positioning handles
            const textX = drawing.points[0].x;
            const textY = drawing.points[0].y;
            const fontSize = drawing.style.fontSize || 16;

            // Measure text
            const tempCanvas = document.createElement('canvas');
            const tempCtx = tempCanvas.getContext('2d');
            tempCtx.font = `${fontSize}px ${drawing.style.fontFamily || 'Arial, sans-serif'}`;
            const textMetrics = tempCtx.measureText(drawing.text || '');
            const textWidth = textMetrics.width;
            const textHeight = fontSize;

            // Non-rotated logical box
            minX = textX;
            minY = textY;
            maxX = textX + textWidth;
            maxY = textY + textHeight;
            centerX = textX + textWidth / 2;
            centerY = textY + textHeight / 2;
            break;
    }

    // Add padding for stroke width (keep text padding minimal so handles are tight)
    let padding;
    if (drawing.tool === 'text') {
        padding = 2; // Tight handles for text
    } else {
        padding = (drawing.style.width || 2) / 2 + 3; // Slightly slimmer padding
    }

    const drawingBounds = {
        x: minX - padding,
        y: minY - padding,
        width: maxX - minX + padding * 2,
        height: maxY - minY + padding * 2,
        centerX: centerX,
        centerY: centerY
    };


    return drawingBounds;
}

// Get drawing bounds WITHOUT padding - for precise edge-to-edge positioning
function getDrawingVisualBounds(drawing) {
    if (!drawing || drawing.points.length < 1) return null;
    if (drawing.tool !== 'text' && drawing.points.length < 2) return null;

    let minX, minY, maxX, maxY, centerX, centerY;

    switch (drawing.tool) {
        case 'circle':
            const ellipseCenter = drawing.points[0];
            const ellipseEdge = drawing.points[1];
            const radiusX = Math.abs(ellipseEdge.x - ellipseCenter.x);
            const radiusY = Math.abs(ellipseEdge.y - ellipseCenter.y);

            minX = ellipseCenter.x - radiusX;
            minY = ellipseCenter.y - radiusY;
            maxX = ellipseCenter.x + radiusX;
            maxY = ellipseCenter.y + radiusY;
            centerX = ellipseCenter.x;
            centerY = ellipseCenter.y;
            break;

        case 'text':
            const textX = drawing.points[0].x;
            const textY = drawing.points[0].y;
            const fontSize = drawing.style.fontSize || 16;

            const tempCanvas = document.createElement('canvas');
            const tempCtx = tempCanvas.getContext('2d');
            tempCtx.font = `${fontSize}px ${drawing.style.fontFamily || 'Arial, sans-serif'}`;
            const textMetrics = tempCtx.measureText(drawing.text || '');
            const textWidth = textMetrics.width;
            const textHeight = fontSize;

            minX = textX;
            minY = textY;
            maxX = textX + textWidth;
            maxY = textY + textHeight;
            centerX = textX + textWidth / 2;
            centerY = textY + textHeight / 2;
            break;

        default:
            minX = Infinity;
            minY = Infinity;
            maxX = -Infinity;
            maxY = -Infinity;

            drawing.points.forEach(point => {
                minX = Math.min(minX, point.x);
                minY = Math.min(minY, point.y);
                maxX = Math.max(maxX, point.x);
                maxY = Math.max(maxY, point.y);
            });

            centerX = (minX + maxX) / 2;
            centerY = (minY + maxY) / 2;
            break;
    }

    // Add stroke width to account for the visual outline
    // Stroke is drawn centered on the path, so half extends outside
    const strokeWidth = drawing.style.width || 2;
    const strokeOffset = strokeWidth / 2;

    // Return exact visual bounds INCLUDING stroke
    return {
        x: minX - strokeOffset,
        y: minY - strokeOffset,
        width: (maxX - minX) + strokeWidth,
        height: (maxY - minY) + strokeWidth,
        left: minX - strokeOffset,
        top: minY - strokeOffset,
        right: maxX + strokeOffset,
        bottom: maxY + strokeOffset,
        centerX: centerX,
        centerY: centerY
    };
}

function createResizeHandle(position, drawingBounds, canvasRect, rotation = 0) {
    const handle = document.createElement('div');
    handle.className = 'resize-handle';

    // Make handles much more visible for testing
    handle.style.cssText = `
        position: absolute;
        width: 12px;
        height: 12px;
        background: blue;
        border: 2px solid white;
        cursor: ${getResizeCursor(position)};
        pointer-events: auto;
        z-index: 99999;
        border-radius: 50%;
        box-shadow: 0 0 5px rgba(0,0,0,0.5);
    `;

    // Use the canvas-specific positioning function
    const handlePos = getCanvasHandlePosition(position, drawingBounds, rotation);

    if (!handlePos || isNaN(handlePos.x) || isNaN(handlePos.y)) {

        return null;
    }

    handle.style.left = (handlePos.x - 6) + 'px';
    handle.style.top = (handlePos.y - 6) + 'px';
    handle.dataset.position = position;



    // Add event listeners for drawing resize (use canvas drawing function)
    handle.addEventListener('mousedown', (e) => {
        e.preventDefault();
        e.stopPropagation();

        startDrawingResize(e, position);
    });

    return handle;
}

function createRotationHandle(drawingBounds, canvasRect, rotation = 0) {
    const handle = document.createElement('div');
    handle.className = 'rotation-handle';

    // Make rotation handle more visible with tooltip
    handle.style.cssText = `
        position: absolute;
        width: 14px;
        height: 14px;
        background: green;
        border: 2px solid white;
        border-radius: 50%;
        cursor: crosshair;
        pointer-events: auto;
        z-index: 99999;
        box-shadow: 0 0 5px rgba(0,0,0,0.5);
        transform: none; /* override grid CSS translateX(-50%) */
    `;

    // Add tooltip for rotation handle
    handle.title = 'Drag to rotate • Hold Shift for 15° angle snapping (0°, 45°, 90°, etc.)';

    // Get the position of the top-center handle first
    const topCenterPos = getCanvasHandlePosition('n', drawingBounds, rotation);

    // Position rotation handle using exact same centering as the n handle, then orbit
    const radians = rotation * (Math.PI / 180);
    const offsetX = Math.sin(radians) * 20; // 20px away in rotated direction
    const offsetY = Math.cos(radians) * 20;

    // Calculate initial position
    let rotateX = topCenterPos.x + offsetX - 7; // Center 14px rotation handle
    let rotateY = topCenterPos.y - offsetY - 7; // Center 14px rotation handle

    // Constrain rotation handle to canvas bounds
    const canvas = document.getElementById('drawing-canvas');
    if (canvas) {
        const canvasBounds = {
            width: canvas.width * zoomLevel,
            height: canvas.height * zoomLevel
        };

        const constrainedPos = constrainHandleToCanvas({ x: rotateX, y: rotateY }, canvasBounds);
        rotateX = constrainedPos.x;
        rotateY = constrainedPos.y;
    }

    handle.style.left = rotateX + 'px';
    handle.style.top = rotateY + 'px';



    // Add event listeners for drawing rotation (use canvas drawing function)
    handle.addEventListener('mousedown', (e) => {
        e.preventDefault();
        e.stopPropagation();

        startDrawingRotation(e);
    });

    return handle;
}

function getResizeCursor(position) {
    const cursors = {
        'nw': 'nw-resize', 'ne': 'ne-resize', 'sw': 'sw-resize', 'se': 'se-resize',
        'n': 'n-resize', 's': 's-resize', 'e': 'e-resize', 'w': 'w-resize'
    };
    return cursors[position] || 'default';
}

// Constrain handle position to canvas bounds
function constrainHandleToCanvas(position, canvasRect) {
    const handleSize = 12; // Handle size for padding
    const padding = handleSize / 2;

    const originalX = position.x;
    const originalY = position.y;

    const constrainedPos = {
        x: Math.max(padding, Math.min(position.x, canvasRect.width - padding)),
        y: Math.max(padding, Math.min(position.y, canvasRect.height - padding))
    };

    // Log when handles are constrained
    if (originalX !== constrainedPos.x || originalY !== constrainedPos.y) {
        console.log(`Handle constrained: (${originalX.toFixed(1)}, ${originalY.toFixed(1)}) -> (${constrainedPos.x.toFixed(1)}, ${constrainedPos.y.toFixed(1)})`);
    }

    return constrainedPos;
}

function getCanvasHandlePosition(position, drawingBounds, rotation = 0) {
    // Canvas-based positioning (like grid elements)
    // Scale the bounds by zoom level for positioning
    const scaledX = drawingBounds.x * zoomLevel;
    const scaledY = drawingBounds.y * zoomLevel;
    const scaledWidth = drawingBounds.width * zoomLevel;
    const scaledHeight = drawingBounds.height * zoomLevel;
    const scaledCenterX = drawingBounds.centerX * zoomLevel;
    const scaledCenterY = drawingBounds.centerY * zoomLevel;

    // Base positions relative to canvas (before rotation)
    const basePositions = {
        'nw': { x: scaledX, y: scaledY },
        'ne': { x: scaledX + scaledWidth, y: scaledY },
        'sw': { x: scaledX, y: scaledY + scaledHeight },
        'se': { x: scaledX + scaledWidth, y: scaledY + scaledHeight },
        'n': { x: scaledCenterX, y: scaledY },
        's': { x: scaledCenterX, y: scaledY + scaledHeight },
        'e': { x: scaledX + scaledWidth, y: scaledCenterY },
        'w': { x: scaledX, y: scaledCenterY }
    };

    const basePos = basePositions[position];
    let finalPos = basePos;

    // Apply rotation if specified
    if (rotation !== 0) {
        const radians = rotation * (Math.PI / 180);
        const cos = Math.cos(radians);
        const sin = Math.sin(radians);

        // Rotate around the center of the drawing
        const relativeX = basePos.x - scaledCenterX;
        const relativeY = basePos.y - scaledCenterY;

        const rotatedX = relativeX * cos - relativeY * sin;
        const rotatedY = relativeX * sin + relativeY * cos;

        finalPos = {
            x: scaledCenterX + rotatedX,
            y: scaledCenterY + rotatedY
        };
    }

    // Get canvas bounds for constraint
    const canvas = document.getElementById('drawing-canvas');
    if (canvas) {
        const canvasRect = canvas.getBoundingClientRect();
        const canvasBounds = {
            width: canvas.width * zoomLevel,
            height: canvas.height * zoomLevel
        };

        // Constrain handle to canvas bounds
        finalPos = constrainHandleToCanvas(finalPos, canvasBounds);
    }

    return finalPos;
}

function removeDrawingHandles() {
    if (drawingHandleContainer) {
        drawingHandleContainer.remove();
        drawingHandleContainer = null;

    }
}

function addMoveCapability(drawing, drawingBounds, canvasRect) {
    // This will be handled by canvas mouse events
    // We'll modify the existing canvas click handler
}

// Canvas drawings use the same multi-selection approach as grid elements
// No separate drag area needed - user drags any selected drawing to move the group

// Canvas group dragging is handled by the existing startDrawingDrag function
// when multiple drawings are selected - no separate functions needed

function startDrawingDrag(e, drawing) {
    e.preventDefault();
    e.stopPropagation();

    // Check if we have a mixed group selection (grid + canvas elements)
    const hasMixedGroup = selectedElements.length > 0 && selectedDrawings.length > 0;
    const hasCanvasGroup = selectedDrawings.length > 1;

    if (hasMixedGroup || hasCanvasGroup) {
        // Set flag to prevent click event interference
        isDraggingCanvasDrawing = true;

        // Use the grid drag system for mixed groups
        isDragging = true;
        movedDuringDrag = false;
        dragStartMouse.x = e.clientX;
        dragStartMouse.y = e.clientY;

        // Store initial positions for group dragging (grid elements)
        if (selectedElements.length > 0) {
            groupDragStartPositions = selectedElements.map(el => ({
                element: el,
                startX: el.x_position,
                startY: el.y_position
            }));
        } else {
            groupDragStartPositions = [];
        }

        // Store initial positions for group dragging (canvas drawings)
        if (selectedDrawings.length > 0) {
            groupDragStartDrawings = selectedDrawings.map(drawing => ({
                drawing: drawing,
                startPoints: drawing.points.map(point => ({ x: point.x, y: point.y }))
            }));

        } else {
            groupDragStartDrawings = [];
        }

        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', stopDrag);
    } else {
        // Set flag to prevent click event interference
        isDraggingCanvasDrawing = true;

        // Single canvas drawing drag
        isDraggingDrawing = true;

        // Convert screen coordinates to canvas coordinates (consistent with other drag functions)
        const canvas = document.getElementById('drawing-canvas');
        const rect = canvas.getBoundingClientRect();
        dragStartPoint = {
            x: (e.clientX - rect.left) / zoomLevel,
            y: (e.clientY - rect.top) / zoomLevel
        };

        document.addEventListener('mousemove', dragDrawing);
        document.addEventListener('mouseup', stopSingleDrawingDrag);
    }
}

function dragDrawing(e) {
    if (!isDraggingDrawing || !selectedDrawing) return;

    const deltaX = e.clientX - dragStartPoint.x;
    const deltaY = e.clientY - dragStartPoint.y;

    // Update all points in the drawing
    selectedDrawing.points.forEach(point => {
        point.x += deltaX;
        point.y += deltaY;
    });

    dragStartPoint = { x: e.clientX, y: e.clientY };

    redrawCanvas();
    updateDrawingHandles();
}

function stopDrag() {
    isDraggingDrawing = false;
    isDraggingCanvasDrawing = false; // Clear canvas drag flag
    document.removeEventListener('mousemove', dragDrawing);
    document.removeEventListener('mouseup', stopDrag);

    if (selectedDrawing) {
        saveToHistory(`Move ${selectedDrawing.tool}`);
    }
}

function startDrawingResize(e, position) {
    e.preventDefault();
    e.stopPropagation();

    isResizingDrawing = true;
    resizeHandle = position;

    // Convert screen coordinates to canvas coordinates
    const canvas = document.getElementById('drawing-canvas');
    const rect = canvas.getBoundingClientRect();
    dragStartPoint = {
        x: (e.clientX - rect.left) / zoomLevel,
        y: (e.clientY - rect.top) / zoomLevel
    };



    // Change cursor to indicate resizing
    document.body.style.cursor = getResizeCursor(position);

    document.addEventListener('mousemove', resizeDrawing);
    document.addEventListener('mouseup', stopDrawingResize);
}

function resizeDrawing(e) {
    if (!isResizingDrawing || !selectedDrawing || !resizeHandle) {
        return;
    }

    console.log(`Resizing ${selectedDrawing.tool} with handle ${resizeHandle}`);

    // Convert screen coordinates to canvas coordinates
    const canvas = document.getElementById('drawing-canvas');
    const rect = canvas.getBoundingClientRect();
    const currentPoint = {
        x: (e.clientX - rect.left) / zoomLevel,
        y: (e.clientY - rect.top) / zoomLevel
    };

    const deltaX = currentPoint.x - dragStartPoint.x;
    const deltaY = currentPoint.y - dragStartPoint.y;



    // Apply resize to drawing points based on handle position and drawing type
    switch (selectedDrawing.tool) {
        case 'rectangle':
        case 'island':
            const point1 = selectedDrawing.points[0];
            const point2 = selectedDrawing.points[1];



            if (resizeHandle.includes('e')) {
                point2.x += deltaX;
            }
            if (resizeHandle.includes('w')) {
                point1.x += deltaX;
            }
            if (resizeHandle.includes('s')) {
                point2.y += deltaY;
            }
            if (resizeHandle.includes('n')) {
                point1.y += deltaY;
            }


            break;

        case 'circle':
            // For circles/ellipses, allow distortion into oval shapes
            // Hold Shift to maintain aspect ratio (perfect circle)
            const circleCenter = selectedDrawing.points[0];
            const circleEdge = selectedDrawing.points[1];

            // Calculate current radii
            const currentRadiusX = Math.abs(circleEdge.x - circleCenter.x);
            const currentRadiusY = Math.abs(circleEdge.y - circleCenter.y);
            const currentAspectRatio = currentRadiusY / currentRadiusX;

            // Convert to bounding box format for easier manipulation
            let circleLeft = circleCenter.x - currentRadiusX;
            let circleTop = circleCenter.y - currentRadiusY;
            let circleRight = circleCenter.x + currentRadiusX;
            let circleBottom = circleCenter.y + currentRadiusY;

            // Check if Shift key is held for aspect ratio constraint
            const maintainAspectRatio = event && event.shiftKey;

            if (maintainAspectRatio) {
                // Maintain aspect ratio - resize proportionally
                let scaleFactor = 1;

                if (resizeHandle.includes('e') || resizeHandle.includes('w')) {
                    // Horizontal resize - scale based on X movement
                    const currentWidth = circleRight - circleLeft;
                    scaleFactor = (currentWidth + Math.abs(deltaX)) / currentWidth;
                    if ((resizeHandle.includes('w') && deltaX > 0) || (resizeHandle.includes('e') && deltaX < 0)) {
                        scaleFactor = (currentWidth - Math.abs(deltaX)) / currentWidth;
                    }
                } else if (resizeHandle.includes('n') || resizeHandle.includes('s')) {
                    // Vertical resize - scale based on Y movement
                    const currentHeight = circleBottom - circleTop;
                    scaleFactor = (currentHeight + Math.abs(deltaY)) / currentHeight;
                    if ((resizeHandle.includes('n') && deltaY > 0) || (resizeHandle.includes('s') && deltaY < 0)) {
                        scaleFactor = (currentHeight - Math.abs(deltaY)) / currentHeight;
                    }
                }

                // Apply proportional scaling
                const newRadiusX = currentRadiusX * scaleFactor;
                const newRadiusY = currentRadiusY * scaleFactor;

                circleLeft = circleCenter.x - newRadiusX;
                circleTop = circleCenter.y - newRadiusY;
                circleRight = circleCenter.x + newRadiusX;
                circleBottom = circleCenter.y + newRadiusY;


            } else {
                // Free resize - allow ellipse distortion
                if (resizeHandle.includes('e')) {
                    circleRight += deltaX;
                }
                if (resizeHandle.includes('w')) {
                    circleLeft += deltaX;
                }
                if (resizeHandle.includes('s')) {
                    circleBottom += deltaY;
                }
                if (resizeHandle.includes('n')) {
                    circleTop += deltaY;
                }


            }

            // Ensure minimum size
            const minSize = 20;
            if (circleRight - circleLeft < minSize) {
                if (resizeHandle.includes('e')) circleRight = circleLeft + minSize;
                if (resizeHandle.includes('w')) circleLeft = circleRight - minSize;
            }
            if (circleBottom - circleTop < minSize) {
                if (resizeHandle.includes('s')) circleBottom = circleTop + minSize;
                if (resizeHandle.includes('n')) circleTop = circleBottom - minSize;
            }

            // Update points to new ellipse bounds
            const newCenterX = (circleLeft + circleRight) / 2;
            const newCenterY = (circleTop + circleBottom) / 2;
            const newRadiusX = (circleRight - circleLeft) / 2;
            const newRadiusY = (circleBottom - circleTop) / 2;

            // Store as center + edge point (edge represents the ellipse bounds)
            selectedDrawing.points[0] = { x: newCenterX, y: newCenterY };
            selectedDrawing.points[1] = { x: newCenterX + newRadiusX, y: newCenterY + newRadiusY };


            break;

        case 'line':
            // For lines, resize by moving the appropriate endpoint
            const lineStartPoint = selectedDrawing.points[0];
            const lineEndPoint = selectedDrawing.points[1];

            if (resizeHandle.includes('nw') || resizeHandle.includes('n') || resizeHandle.includes('w')) {
                // Moving start point
                if (resizeHandle.includes('w') || resizeHandle.includes('nw')) {
                    lineStartPoint.x += deltaX;
                }
                if (resizeHandle.includes('n') || resizeHandle.includes('nw')) {
                    lineStartPoint.y += deltaY;
                }
            } else {
                // Moving end point
                if (resizeHandle.includes('e') || resizeHandle.includes('se')) {
                    lineEndPoint.x += deltaX;
                }
                if (resizeHandle.includes('s') || resizeHandle.includes('se')) {
                    lineEndPoint.y += deltaY;
                }
            }
            break;

        case 'road':
            // For roads, extend/shorten from the specific side being dragged along the road's length direction
            const roadStartPoint = selectedDrawing.points[0];
            const roadEndPoint = selectedDrawing.points[1];

            console.log(`Road resize with handle: ${resizeHandle}`);

            if (resizeHandle === 'w') {
                // West handle - extend/shorten the westernmost point (lowest X)
                if (roadStartPoint.x <= roadEndPoint.x) {
                    roadStartPoint.x += deltaX;
                } else {
                    roadEndPoint.x += deltaX;
                }
                console.log(`Extending road west by deltaX: ${deltaX.toFixed(1)}`);

            } else if (resizeHandle === 'e') {
                // East handle - extend/shorten the easternmost point (highest X)
                if (roadStartPoint.x >= roadEndPoint.x) {
                    roadStartPoint.x += deltaX;
                } else {
                    roadEndPoint.x += deltaX;
                }
                console.log(`Extending road east by deltaX: ${deltaX.toFixed(1)}`);

            } else if (resizeHandle === 'n') {
                // North handle - extend/shorten the northernmost point (lowest Y)
                if (roadStartPoint.y <= roadEndPoint.y) {
                    roadStartPoint.y += deltaY;
                } else {
                    roadEndPoint.y += deltaY;
                }
                console.log(`Extending road north by deltaY: ${deltaY.toFixed(1)}`);

            } else if (resizeHandle === 's') {
                // South handle - extend/shorten the southernmost point (highest Y)
                if (roadStartPoint.y >= roadEndPoint.y) {
                    roadStartPoint.y += deltaY;
                } else {
                    roadEndPoint.y += deltaY;
                }
                console.log(`Extending road south by deltaY: ${deltaY.toFixed(1)}`);
            }


            break;

        case 'freehand':
            // For freehand, scale all points relative to the center
            const bounds = getDrawingBounds(selectedDrawing);
            const drawingResizeCenterX = bounds.centerX;
            const drawingResizeCenterY = bounds.centerY;

            // Calculate scale factor based on handle movement
            let scaleX = 1, scaleY = 1;
            if (resizeHandle.includes('e')) scaleX += deltaX / bounds.width;
            if (resizeHandle.includes('w')) scaleX -= deltaX / bounds.width;
            if (resizeHandle.includes('s')) scaleY += deltaY / bounds.height;
            if (resizeHandle.includes('n')) scaleY -= deltaY / bounds.height;

            // Apply scaling to all points
            selectedDrawing.points.forEach(point => {
                const relativeX = point.x - drawingResizeCenterX;
                const relativeY = point.y - drawingResizeCenterY;
                point.x = drawingResizeCenterX + relativeX * scaleX;
                point.y = drawingResizeCenterY + relativeY * scaleY;
            });


            break;

        case 'text':
            // For text, calculate scale factor based on handle movement
            const textBounds = getDrawingBounds(selectedDrawing);
            let textScaleX = 1, textScaleY = 1;

            if (resizeHandle.includes('e')) textScaleX += deltaX / textBounds.width;
            if (resizeHandle.includes('w')) textScaleX -= deltaX / textBounds.width;
            if (resizeHandle.includes('s')) textScaleY += deltaY / textBounds.height;
            if (resizeHandle.includes('n')) textScaleY -= deltaY / textBounds.height;

            // Corner handles: resize text (change font size)
            if (resizeHandle === 'nw' || resizeHandle === 'ne' || resizeHandle === 'sw' || resizeHandle === 'se') {
                const currentFontSize = selectedDrawing.style.fontSize || 16;
                const scaleFactor = Math.max(Math.abs(textScaleX), Math.abs(textScaleY));
                const newFontSize = Math.max(8, Math.min(72, currentFontSize * scaleFactor));
                selectedDrawing.style.fontSize = newFontSize;
            } else {
                // Edge handles: move text position
                selectedDrawing.points[0].x += deltaX;
                selectedDrawing.points[0].y += deltaY;
            }
            break;

        default:

            break;
    }

    redrawCanvas();

    // Draw grid snap preview for roads during handle resize (all handles)
    if (selectedDrawing && selectedDrawing.tool === 'road') {
        const canvas = document.getElementById('drawing-canvas');
        const ctx = canvas.getContext('2d');

        // Show snap preview for the endpoint being moved by each handle
        const startPoint = selectedDrawing.points[0];
        const endPoint = selectedDrawing.points[1];
        let pointToSnap = null;

        if (resizeHandle === 'w') {
            // West handle - snap the westernmost point
            pointToSnap = (startPoint.x <= endPoint.x) ? startPoint : endPoint;
        } else if (resizeHandle === 'e') {
            // East handle - snap the easternmost point
            pointToSnap = (startPoint.x >= endPoint.x) ? startPoint : endPoint;
        } else if (resizeHandle === 'n') {
            // North handle - snap the northernmost point
            pointToSnap = (startPoint.y <= endPoint.y) ? startPoint : endPoint;
        } else if (resizeHandle === 's') {
            // South handle - snap the southernmost point
            pointToSnap = (startPoint.y >= endPoint.y) ? startPoint : endPoint;
        }

        if (pointToSnap) {
            const snappedPoint = snapToGrid(pointToSnap);
            if (Math.abs(snappedPoint.x - pointToSnap.x) > 0.1 || Math.abs(snappedPoint.y - pointToSnap.y) > 0.1) {
                ctx.save();
                ctx.strokeStyle = '#00ff00';
                ctx.lineWidth = 2;
                ctx.setLineDash([3, 3]);
                ctx.beginPath();
                ctx.arc(snappedPoint.x, snappedPoint.y, 8, 0, 2 * Math.PI);
                ctx.stroke();
                ctx.restore();
            }
        }
    }

    updateDrawingHandles();

    // Show size indicator during resize
    if (selectedDrawing && !['road', 'text', 'freehand'].includes(selectedDrawing.tool)) {
        const bounds = getDrawingBounds(selectedDrawing);
        showSizeIndicator(selectedDrawing, bounds, selectedDrawing.tool);
    }

    dragStartPoint = currentPoint;
}

function stopDrawingResize() {
    isResizingDrawing = false;
    resizeHandle = null;
    document.body.style.cursor = '';
    document.removeEventListener('mousemove', resizeDrawing);
    document.removeEventListener('mouseup', stopDrawingResize);

    // Apply grid snapping to roads after resize is complete
    if (selectedDrawing && selectedDrawing.tool === 'road' && selectedDrawing.points.length >= 2) {
        console.log('Applying grid snap after road resize...');

        // Snap the endpoint that was moved by the specific handle
        const roadStartPoint = selectedDrawing.points[0];
        const roadEndPoint = selectedDrawing.points[1];

        if (resizeHandle === 'w') {
            // West handle - snap the westernmost point
            if (roadStartPoint.x <= roadEndPoint.x) {
                selectedDrawing.points[0] = snapToGrid(roadStartPoint);
                console.log('Snapping start point (W handle - westernmost)');
            } else {
                selectedDrawing.points[1] = snapToGrid(roadEndPoint);
                console.log('Snapping end point (W handle - westernmost)');
            }
        } else if (resizeHandle === 'e') {
            // East handle - snap the easternmost point
            if (roadStartPoint.x >= roadEndPoint.x) {
                selectedDrawing.points[0] = snapToGrid(roadStartPoint);
                console.log('Snapping start point (E handle - easternmost)');
            } else {
                selectedDrawing.points[1] = snapToGrid(roadEndPoint);
                console.log('Snapping end point (E handle - easternmost)');
            }
        } else if (resizeHandle === 'n') {
            // North handle - snap the northernmost point
            if (roadStartPoint.y <= roadEndPoint.y) {
                selectedDrawing.points[0] = snapToGrid(roadStartPoint);
                console.log('Snapping start point (N handle - northernmost)');
            } else {
                selectedDrawing.points[1] = snapToGrid(roadEndPoint);
                console.log('Snapping end point (N handle - northernmost)');
            }
        } else if (resizeHandle === 's') {
            // South handle - snap the southernmost point
            if (roadStartPoint.y >= roadEndPoint.y) {
                selectedDrawing.points[0] = snapToGrid(roadStartPoint);
                console.log('Snapping start point (S handle - southernmost)');
            } else {
                selectedDrawing.points[1] = snapToGrid(roadEndPoint);
                console.log('Snapping end point (S handle - southernmost)');
            }
        }

        redrawCanvas();
        updateDrawingHandles();
    }

    if (selectedDrawing) {
        saveToHistory(`Resize ${selectedDrawing.tool}`);

        // Update group data if this drawing was selected from group manager
        if (selectedFromGroupManager) {
            // Update all relevant data in the original data
            selectedFromGroupManager.originalData.points = JSON.parse(JSON.stringify(selectedDrawing.points));
            if (selectedDrawing.style) {
                selectedFromGroupManager.originalData.style = JSON.parse(JSON.stringify(selectedDrawing.style));
            }
        }
    }
}

// Drawing drag functions
function startSingleDrawingDrag(e) {
    if (!selectedDrawing) return;

    isDraggingDrawing = true;

    // Convert screen coordinates to canvas coordinates
    const canvas = document.getElementById('drawing-canvas');
    const rect = canvas.getBoundingClientRect();
    dragStartPoint = {
        x: (e.clientX - rect.left) / zoomLevel,
        y: (e.clientY - rect.top) / zoomLevel
    };


    document.body.style.cursor = 'grabbing';

    document.addEventListener('mousemove', dragDrawing);
    document.addEventListener('mouseup', stopSingleDrawingDrag);
}

function dragDrawing(e) {
    if (!isDraggingDrawing || !selectedDrawing) return;

    // Convert screen coordinates to canvas coordinates
    const canvas = document.getElementById('drawing-canvas');
    const rect = canvas.getBoundingClientRect();
    const currentPoint = {
        x: (e.clientX - rect.left) / zoomLevel,
        y: (e.clientY - rect.top) / zoomLevel
    };

    const deltaX = currentPoint.x - dragStartPoint.x;
    const deltaY = currentPoint.y - dragStartPoint.y;

    // Move all points of the drawing
    selectedDrawing.points.forEach(point => {
        point.x += deltaX;
        point.y += deltaY;
    });



    dragStartPoint = currentPoint;
    redrawCanvas();

    // Draw grid snap preview for roads during drag - maintain direction
    if (selectedDrawing && selectedDrawing.tool === 'road' && selectedDrawing.points.length >= 2) {
        const canvas = document.getElementById('drawing-canvas');
        const ctx = canvas.getContext('2d');

        const currentStart = selectedDrawing.points[0];
        const currentEnd = selectedDrawing.points[1];

        // Calculate road direction
        const roadDx = currentEnd.x - currentStart.x;
        const roadDy = currentEnd.y - currentStart.y;

        // Snap only the start point
        const snappedStart = snapToGrid(currentStart);

        // Calculate end point to maintain direction
        const snappedEnd = {
            x: snappedStart.x + roadDx,
            y: snappedStart.y + roadDy
        };

        // Draw snap preview
        ctx.save();

        // Start point - blue dashed circle (only if it will snap)
        if (Math.abs(snappedStart.x - currentStart.x) > 0.1 || Math.abs(snappedStart.y - currentStart.y) > 0.1) {
            ctx.strokeStyle = '#0066ff'; // Blue for start point snap
            ctx.lineWidth = 2;
            ctx.setLineDash([4, 4]);
            ctx.beginPath();
            ctx.arc(snappedStart.x, snappedStart.y, 10, 0, 2 * Math.PI);
            ctx.stroke();

            // Draw preview road in new position
            ctx.strokeStyle = '#ffaa00'; // Orange for preview road
            ctx.lineWidth = 2;
            ctx.setLineDash([3, 3]);
            ctx.beginPath();
            ctx.moveTo(snappedStart.x, snappedStart.y);
            ctx.lineTo(snappedEnd.x, snappedEnd.y);
            ctx.stroke();

            // Small dot at end point to show where it will be
            ctx.fillStyle = '#ffaa00';
            ctx.beginPath();
            ctx.arc(snappedEnd.x, snappedEnd.y, 3, 0, 2 * Math.PI);
            ctx.fill();
        }

        ctx.restore();
    }

    updateDrawingHandles();
}

function stopSingleDrawingDrag() {
    isDraggingDrawing = false;
    isDraggingCanvasDrawing = false; // Reset the canvas drag flag
    document.body.style.cursor = '';
    document.removeEventListener('mousemove', dragDrawing);
    document.removeEventListener('mouseup', stopSingleDrawingDrag);

    // Apply grid snapping when dragging roads ends - maintain direction
    if (selectedDrawing && selectedDrawing.tool === 'road' && selectedDrawing.points.length >= 2) {
        console.log('Applying grid snap after road drag - maintaining direction...');

        // Calculate original road direction and length
        const originalStart = selectedDrawing.points[0];
        const originalEnd = selectedDrawing.points[1];
        const roadDx = originalEnd.x - originalStart.x;
        const roadDy = originalEnd.y - originalStart.y;
        const roadLength = Math.sqrt(roadDx * roadDx + roadDy * roadDy);

        // Snap the start point to grid
        const snappedStart = snapToGrid(originalStart);

        // Calculate end point to maintain direction and length
        const snappedEnd = {
            x: snappedStart.x + roadDx,
            y: snappedStart.y + roadDy
        };

        selectedDrawing.points[0] = snappedStart;
        selectedDrawing.points[1] = snappedEnd;

        console.log(`Road direction maintained: start(${snappedStart.x}, ${snappedStart.y}) end(${snappedEnd.x}, ${snappedEnd.y})`);

        redrawCanvas();
        updateDrawingHandles();
    }

    if (selectedDrawing) {
        saveToHistory(`Move ${selectedDrawing.tool}`);

        // Update group data if this drawing was selected from group manager
        if (selectedFromGroupManager) {
            // Update all points in the original data
            selectedFromGroupManager.originalData.points = JSON.parse(JSON.stringify(selectedDrawing.points));
        }
    }


}

function startDrawingRotation(e) {
    e.preventDefault();
    e.stopPropagation();

    isRotatingDrawing = true;
    document.body.style.cursor = 'crosshair';

    // Store initial rotation for relative calculations
    selectedDrawing.initialRotation = selectedDrawing.rotation || 0;

    // Get canvas coordinates for rotation calculation
    const canvas = document.getElementById('drawing-canvas');
    const rect = canvas.getBoundingClientRect();
    const canvasX = (e.clientX - rect.left) / zoomLevel;
    const canvasY = (e.clientY - rect.top) / zoomLevel;

    const drawingRotationBounds = getDrawingBounds(selectedDrawing);
    const drawingCenterX = drawingRotationBounds.centerX;
    const drawingCenterY = drawingRotationBounds.centerY;

    rotationStartAngle = Math.atan2(canvasY - drawingCenterY, canvasX - drawingCenterX);



    document.addEventListener('mousemove', rotateDrawing);
    document.addEventListener('mouseup', stopDrawingRotation);
}

function rotateDrawing(e) {
    if (!isRotatingDrawing || !selectedDrawing) return;

    // Get canvas coordinates for rotation calculation
    const canvas = document.getElementById('drawing-canvas');
    const rect = canvas.getBoundingClientRect();
    const canvasX = (e.clientX - rect.left) / zoomLevel;
    const canvasY = (e.clientY - rect.top) / zoomLevel;

    // Get drawing center
    const drawingRotateBounds = getDrawingBounds(selectedDrawing);
    const drawingRotateCenterX = drawingRotateBounds.centerX;
    const drawingRotateCenterY = drawingRotateBounds.centerY;

    const currentAngle = Math.atan2(canvasY - drawingRotateCenterY, canvasX - drawingRotateCenterX);

    // Calculate absolute rotation angle in degrees
    const currentAngleDegrees = currentAngle * (180 / Math.PI);
    const startAngleDegrees = rotationStartAngle * (180 / Math.PI);

    // Calculate the total rotation from start
    let totalRotation = currentAngleDegrees - startAngleDegrees;

    // Normalize to prevent jumps
    if (totalRotation > 180) totalRotation -= 360;
    if (totalRotation < -180) totalRotation += 360;

    // Apply to drawing (start from initial rotation)
    if (!selectedDrawing.initialRotation && selectedDrawing.initialRotation !== 0) {
        selectedDrawing.initialRotation = selectedDrawing.rotation || 0;
    }

    let newRotation = selectedDrawing.initialRotation + totalRotation;

    // Apply angle snapping if Shift key is held
    if (e.shiftKey) {
        const originalRotation = newRotation;
        newRotation = snapRotationAngle(newRotation, 8); // 8-degree snap threshold
        console.log('Applying rotation angle snap...');

        // Show visual feedback for angle snapping
        showAngleSnapFeedback(newRotation, originalRotation !== newRotation);
    } else {
        // Hide angle snap feedback when not using Shift
        hideAngleSnapFeedback();
    }

    selectedDrawing.rotation = newRotation;

    // Redraw to show rotation
    redrawCanvas();

    // Update handles during rotation (like grid system does)
    updateDrawingHandles();
}

function stopDrawingRotation() {
    isRotatingDrawing = false;
    document.body.style.cursor = '';
    document.removeEventListener('mousemove', rotateDrawing);
    document.removeEventListener('mouseup', stopDrawingRotation);

    // Hide angle snap feedback
    hideAngleSnapFeedback();

    if (selectedDrawing) {
        // Clean up temporary rotation tracking
        delete selectedDrawing.initialRotation;
        saveToHistory(`Rotate ${selectedDrawing.tool}`);
    }

    // Update handles after rotation is complete
    updateDrawingHandles();

    // Show size indicator for selected drawing
    if (selectedDrawing && !['road', 'text', 'freehand'].includes(selectedDrawing.tool)) {
        const bounds = getDrawingBounds(selectedDrawing);
        showSizeIndicator(selectedDrawing, bounds, selectedDrawing.tool);
    }
}

function updateDrawingHandles() {
    if (selectedDrawing) {
        addDrawingHandles(selectedDrawing);
    }
}

function applyResize(drawing, handle, deltaX, deltaY) {
    if (drawing.points.length < 2) return;

    const point1 = drawing.points[0];
    const point2 = drawing.points[1];

    switch (handle) {
        case 'nw':
            point1.x += deltaX;
            point1.y += deltaY;
            break;
        case 'ne':
            point2.x += deltaX;
            point1.y += deltaY;
            break;
        case 'sw':
            point1.x += deltaX;
            point2.y += deltaY;
            break;
        case 'se':
            point2.x += deltaX;
            point2.y += deltaY;
            break;
        case 'n':
            point1.y += deltaY;
            break;
        case 's':
            point2.y += deltaY;
            break;
        case 'e':
            point2.x += deltaX;
            break;
        case 'w':
            point1.x += deltaX;
            break;
    }

    // For circles, maintain circular shape
    if (drawing.tool === 'circle') {
        const drawingCircleCenterX = point1.x;
        const drawingCircleCenterY = point1.y;
        const radius = Math.sqrt(
            Math.pow(point2.x - drawingCircleCenterX, 2) +
            Math.pow(point2.y - drawingCircleCenterY, 2)
        );

        // Update second point to maintain circle
        const angle = Math.atan2(point2.y - drawingCircleCenterY, point2.x - drawingCircleCenterX);
        point2.x = drawingCircleCenterX + radius * Math.cos(angle);
        point2.y = drawingCircleCenterY + radius * Math.sin(angle);
    }
}

function hideRoadEditor() {
    const editor = document.getElementById('road-editor');
    if (editor) {
        editor.remove();
    }
    selectedRoad = null;
}

function updateRoadName(name) {


    // Find the road by ID if selectedRoad is null
    if (!selectedRoad) {
        const editor = document.getElementById('road-editor');
        if (editor && editor.dataset.roadId) {
            selectedRoad = drawings.find(d => d.id === editor.dataset.roadId);

        }
    }

    if (!selectedRoad) {

        return;
    }

    selectedRoad.name = name;

    redrawCanvas();
    saveToHistory('Update road name');
}

function updateRoadNamePosition(position) {


    // Find the road by ID if selectedRoad is null
    if (!selectedRoad) {
        const editor = document.getElementById('road-editor');
        if (editor && editor.dataset.roadId) {
            selectedRoad = drawings.find(d => d.id === editor.dataset.roadId);
        }
    }

    if (!selectedRoad) {

        return;
    }

    selectedRoad.namePosition = position;

    redrawCanvas();
    saveToHistory('Update road name position');
}

function updateRoadWidth(sliderValue, saveHistory = false) {
    // Convert slider value (0, 1, 2) to actual width (20, 40, 60)
    const widthMap = { 0: 20, 1: 40, 2: 60 };
    const actualWidth = widthMap[parseInt(sliderValue)] || 20;

    // Find the road by ID if selectedRoad is null
    if (!selectedRoad) {
        const editor = document.getElementById('road-editor');
        if (editor && editor.dataset.roadId) {
            selectedRoad = drawings.find(d => d.id === editor.dataset.roadId);
        }
    }

    if (!selectedRoad) {
        return;
    }

    const oldWidth = selectedRoad.style.width;
    selectedRoad.style.width = actualWidth;

    const display = document.getElementById('road-width-display');
    if (display) {
        display.textContent = actualWidth;
    }
    redrawCanvas();

    // Only save to history when explicitly requested (onchange, not oninput)
    if (saveHistory) {
        saveToHistory('Update road width');
    }
}

function updateRoadColor(color) {
    // Find the road by ID if selectedRoad is null
    if (!selectedRoad) {
        const editor = document.getElementById('road-editor');
        if (editor && editor.dataset.roadId) {
            selectedRoad = drawings.find(d => d.id === editor.dataset.roadId);
        }
    }

    if (!selectedRoad) {
        return;
    }

    selectedRoad.style.color = color;
    redrawCanvas();
    saveToHistory('Update road color');
}

function updateRoadTextColor(color) {
    // Find the road by ID if selectedRoad is null
    if (!selectedRoad) {
        const editor = document.getElementById('road-editor');
        if (editor && editor.dataset.roadId) {
            selectedRoad = drawings.find(d => d.id === editor.dataset.roadId);
        }
    }

    if (!selectedRoad) {
        return;
    }

    selectedRoad.textColor = color;
    redrawCanvas();
    saveToHistory('Update road text color');
}

// General drawing update functions
function updateSelectedDrawingColor(color) {


    if (!selectedDrawing) {
        const editor = document.getElementById('drawing-editor');
        if (editor && editor.dataset.drawingId) {
            selectedDrawing = drawings.find(d => d.id === editor.dataset.drawingId);
            console.log('Recovered selectedDrawing:', selectedDrawing);
        }
    }

    if (!selectedDrawing) {

        return;
    }

    const oldColor = selectedDrawing.style.color;
    selectedDrawing.style.color = color;

    // Update group data if this drawing was selected from group manager
    if (selectedFromGroupManager) {
        selectedFromGroupManager.originalData.style.color = color;
    }

    redrawCanvas();
    saveToHistory(`Update ${selectedDrawing.tool} color`);
}

function updateSelectedDrawingWidth(width, saveHistory = false) {


    if (!selectedDrawing) {

        const editor = document.getElementById('drawing-editor');
        if (editor && editor.dataset.drawingId) {
            selectedDrawing = drawings.find(d => d.id === editor.dataset.drawingId);

        }
    }

    if (!selectedDrawing) {

        return;
    }

    // Text uses the slider for font size
    if (selectedDrawing.tool === 'text') {
        const oldSize = selectedDrawing.style.fontSize || 16;
        selectedDrawing.style.fontSize = parseInt(width);

        // Update group data if this drawing was selected from group manager
        if (selectedFromGroupManager) {
            selectedFromGroupManager.originalData.style.fontSize = parseInt(width);
        }
    } else {
        const fillCheck = document.getElementById('drawing-editor-fill-check');
        const isFilled = fillCheck && fillCheck.checked;
        const isShapeType = selectedDrawing.tool === 'rectangle' || selectedDrawing.tool === 'circle' || selectedDrawing.tool === 'island';

        if (isFilled && isShapeType) {
            // For filled shapes, adjust the size (scale the shape)
            updateShapeSize(selectedDrawing, parseInt(width));

        } else {
            // For non-filled shapes or lines, adjust the stroke width
            const oldWidth = selectedDrawing.style.width;
            selectedDrawing.style.width = parseInt(width);

            // Update group data if this drawing was selected from group manager
            if (selectedFromGroupManager) {
                selectedFromGroupManager.originalData.style.width = parseInt(width);
            }
        }
    }

    const display = document.getElementById('drawing-editor-width-display');
    if (display) {
        display.textContent = width;
        display.textContent = width;
    }
    redrawCanvas();
    // Ensure handle overlay follows visual changes
    updateDrawingHandles();

    // Only save to history when explicitly requested (onchange, not oninput)
    if (saveHistory) {
        const label = (selectedDrawing.tool === 'text') ? 'font size' : ((isFilled && isShapeType) ? 'size' : 'width');
        saveToHistory(`Update ${selectedDrawing.tool} ${label}`);
    }
}

function updateSelectedDrawingFill(fill) {


    if (!selectedDrawing) {

        const editor = document.getElementById('drawing-editor');
        if (editor && editor.dataset.drawingId) {
            selectedDrawing = drawings.find(d => d.id === editor.dataset.drawingId);

        }
    }

    if (!selectedDrawing) {

        return;
    }

    const oldFill = selectedDrawing.style.fill;
    selectedDrawing.style.fill = fill;

    redrawCanvas();
    saveToHistory(`Update ${selectedDrawing.tool} fill`);
}

function deleteSelectedDrawing() {
    if (!selectedDrawing) {
        const editor = document.getElementById('drawing-editor');
        if (editor && editor.dataset.drawingId) {
            selectedDrawing = drawings.find(d => d.id === editor.dataset.drawingId);
        }
    }

    if (!selectedDrawing) return;

    if (confirm(`Delete this ${selectedDrawing.tool}?`)) {
        const toolName = selectedDrawing.tool;
        const index = drawings.indexOf(selectedDrawing);
        if (index > -1) {
            drawings.splice(index, 1);
            redrawCanvas();
            hideGeneralDrawingEditor();
            removeDrawingHandles();
            selectedDrawing = null;
            saveToHistory(`Delete ${toolName}`);
        }
    }
}

function deleteSelectedRoad() {
    // Find the road by ID if selectedRoad is null
    if (!selectedRoad) {
        const editor = document.getElementById('road-editor');
        if (editor && editor.dataset.roadId) {
            selectedRoad = drawings.find(d => d.id === editor.dataset.roadId);
        }
    }

    if (!selectedRoad) return;

    if (confirm('Delete this road?')) {
        const index = drawings.indexOf(selectedRoad);
        if (index > -1) {
            drawings.splice(index, 1);
            redrawCanvas();
            hideRoadEditor();
            removeDrawingHandles();
            selectedRoad = null;
            selectedDrawing = null;
            saveToHistory('Delete road');
        }
    }
}

// General function to delete any drawing object at a point
function deleteDrawingAtPoint(clickPoint) {
    for (let i = drawings.length - 1; i >= 0; i--) {
        const drawing = drawings[i];
        if (isPointOnDrawing(clickPoint, drawing)) {
            if (confirm(`Delete this ${drawing.tool}?`)) {
                drawings.splice(i, 1);
                redrawCanvas();
                saveToHistory(`Delete ${drawing.tool}`);
                return true;
            }
        }
    }
    return false;
}

function isPointOnDrawing(point, drawing) {
    switch (drawing.tool) {
        case 'road':
            return isPointOnRoad(point, drawing);
        case 'line':
            return isPointOnLine(point, drawing);
        case 'rectangle':
        case 'island':
            return isPointInRectangle(point, drawing);
        case 'circle':
            return isPointInCircle(point, drawing);
        case 'freehand':
            return isPointOnFreehand(point, drawing);
        case 'text':
            return isPointOnText(point, drawing);
        default:
            return false;
    }
}

function isPointOnLine(point, line) {
    if (line.points.length < 2) return false;

    const start = line.points[0];
    const end = line.points[1];
    const lineWidth = line.style.width || 2;

    // Calculate distance from point to line segment
    const A = point.x - start.x;
    const B = point.y - start.y;
    const C = end.x - start.x;
    const D = end.y - start.y;

    const dot = A * C + B * D;
    const lenSq = C * C + D * D;

    if (lenSq === 0) return false;

    const param = dot / lenSq;

    let xx, yy;
    if (param < 0) {
        xx = start.x;
        yy = start.y;
    } else if (param > 1) {
        xx = end.x;
        yy = end.y;
    } else {
        xx = start.x + param * C;
        yy = start.y + param * D;
    }

    const dx = point.x - xx;
    const dy = point.y - yy;
    const distance = Math.sqrt(dx * dx + dy * dy);

    return distance <= lineWidth / 2 + 5; // 5px tolerance
}

function isPointInRectangle(point, rect) {
    if (rect.points.length < 2) return false;

    const start = rect.points[0];
    const end = rect.points[1];
    const minX = Math.min(start.x, end.x);
    const maxX = Math.max(start.x, end.x);
    const minY = Math.min(start.y, end.y);
    const maxY = Math.max(start.y, end.y);

    return point.x >= minX && point.x <= maxX && point.y >= minY && point.y <= maxY;
}

function isPointInCircle(point, circle) {
    if (circle.points.length < 2) return false;

    const center = circle.points[0];
    const edge = circle.points[1];
    const radius = Math.sqrt(
        Math.pow(edge.x - center.x, 2) + Math.pow(edge.y - center.y, 2)
    );

    const distance = Math.sqrt(
        Math.pow(point.x - center.x, 2) + Math.pow(point.y - center.y, 2)
    );

    return distance <= radius;
}

function isPointOnFreehand(point, freehand) {
    if (freehand.points.length < 2) return false;

    const tolerance = (freehand.style.width || 2) / 2 + 5;

    for (let i = 0; i < freehand.points.length - 1; i++) {
        const start = freehand.points[i];
        const end = freehand.points[i + 1];

        // Check distance to line segment
        const A = point.x - start.x;
        const B = point.y - start.y;
        const C = end.x - start.x;
        const D = end.y - start.y;

        const dot = A * C + B * D;
        const lenSq = C * C + D * D;

        if (lenSq === 0) continue;

        const param = Math.max(0, Math.min(1, dot / lenSq));
        const xx = start.x + param * C;
        const yy = start.y + param * D;

        const dx = point.x - xx;
        const dy = point.y - yy;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance <= tolerance) return true;
    }

    return false;
}

function isPointOnText(point, textDrawing) {
    if (!textDrawing.text || textDrawing.points.length < 1) return false;

    const textX = textDrawing.points[0].x;
    const textY = textDrawing.points[0].y;
    const fontSize = textDrawing.style.fontSize || 16;

    // Create a temporary canvas to measure text dimensions
    const tempCanvas = document.createElement('canvas');
    const tempCtx = tempCanvas.getContext('2d');
    tempCtx.font = `${fontSize}px ${textDrawing.style.fontFamily || 'Arial, sans-serif'}`;
    const textMetrics = tempCtx.measureText(textDrawing.text);
    const textWidth = textMetrics.width;
    const textHeight = fontSize;



    // If text is rotated, transform the click point to text's local coordinate system
    if (textDrawing.rotation) {
        const rotation = textDrawing.rotation * (Math.PI / 180);
        const centerX = textX + textWidth / 2;
        const centerY = textY + textHeight / 2;

        // Transform click point by inverse rotation
        const cos = Math.cos(-rotation);
        const sin = Math.sin(-rotation);
        const relativeX = point.x - centerX;
        const relativeY = point.y - centerY;

        const transformedX = centerX + (relativeX * cos - relativeY * sin);
        const transformedY = centerY + (relativeX * sin + relativeY * cos);



        // Check if transformed point is within original text bounds
        const hit = transformedX >= textX &&
                    transformedX <= textX + textWidth &&
                    transformedY >= textY &&
                    transformedY <= textY + textHeight;


        return hit;
    } else {
        // No rotation, use original bounds check
        const hit = point.x >= textX &&
                    point.x <= textX + textWidth &&
                    point.y >= textY &&
                    point.y <= textY + textHeight;


        return hit;
    }
}

// ===== DRAWING SYSTEM FUNCTIONS =====



function selectDrawingColor(color) {
    drawingColor = color;

    // Update active color button
    document.querySelectorAll('.color-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-color="${color}"]`).classList.add('active');
}

function updateDrawingWidth(width) {
    drawingWidth = parseInt(width);

    // Update both old and new toolbar displays
    const widthDisplay = document.getElementById('width-display');
    if (widthDisplay) {
        widthDisplay.textContent = width + 'px';
    }

    const widthSlider = document.getElementById('drawing-width');
    if (widthSlider) {
        widthSlider.value = width;
    }
}

function updateDrawingFill(fill) {
    drawingFill = fill;
    document.getElementById('drawing-fill').checked = fill;
}

function clearAllDrawings() {
    if (confirm('Are you sure you want to clear all drawings?')) {
        drawings = [];
        redrawCanvas();
        saveToHistory('Clear all drawings');
    }
}

// ===== COMPACT TOOLBAR FUNCTIONS =====

function updateDrawingColor(color) {
    drawingColor = color;
    document.getElementById('drawing-color').value = color;
}

function setQuickColor(color) {
    drawingColor = color;
    document.getElementById('drawing-color').value = color;

    // Update visual feedback
    document.querySelectorAll('.compact-drawing-toolbar .tool-btn').forEach(btn => {
        if (btn.onclick && btn.onclick.toString().includes(`setQuickColor('${color}')`)) {
            btn.style.transform = 'scale(0.9)';
            setTimeout(() => {
                btn.style.transform = '';
            }, 150);
        }
    });
}

function toggleDrawingFill() {
    drawingFill = !drawingFill;
    const fillBtn = document.getElementById('fill-toggle');

    if (drawingFill) {
        fillBtn.classList.add('active');
        fillBtn.innerHTML = '<i class="fas fa-fill"></i>';
    } else {
        fillBtn.classList.remove('active');
        fillBtn.innerHTML = '<i class="fas fa-fill-drip"></i>';
    }
}

function selectDrawingTool(tool) {


    // Deselect all tools in both old and new toolbars
    document.querySelectorAll('[id^="tool-"]').forEach(btn => {
        btn.classList.remove('drawing-tool-active', 'active');
    });

    // Select new tool
    if (currentDrawingTool === tool) {
        // Clicking same tool deactivates drawing mode
        currentDrawingTool = null;
        drawingMode = false;
        document.getElementById('drawing-canvas').style.pointerEvents = 'none';

    } else {
        currentDrawingTool = tool;
        drawingMode = true;

        // Activate button in compact toolbar
        const compactBtn = document.querySelector(`.compact-drawing-toolbar #tool-${tool}`);
        if (compactBtn) {
            compactBtn.classList.add('active');
        }

        const drawingCanvas = document.getElementById('drawing-canvas');
        drawingCanvas.style.pointerEvents = 'auto';


        // Set tool-specific defaults
        if (tool === 'road') {
            drawingColor = '#666666';
            drawingWidth = 20;
            drawingFill = false;
        } else if (tool === 'island') {
            drawingColor = '#28a745';
            drawingWidth = 10;
            drawingFill = true;
        }


    }

    // Update toolbar controls
    updateToolbarControls();
}

function updateToolbarControls() {
    // Update color picker
    const colorPicker = document.getElementById('drawing-color');
    if (colorPicker) {
        colorPicker.value = drawingColor;
    }

    // Update width slider and display
    const widthSlider = document.getElementById('drawing-width');
    const widthDisplay = document.getElementById('width-display');
    if (widthSlider && widthDisplay) {
        widthSlider.value = drawingWidth;
        widthDisplay.textContent = drawingWidth + 'px';
    }

    // Update fill toggle
    const fillToggle = document.getElementById('fill-toggle');
    if (fillToggle) {
        if (drawingFill) {
            fillToggle.classList.add('active');
            fillToggle.innerHTML = '<i class="fas fa-fill"></i>';
        } else {
            fillToggle.classList.remove('active');
            fillToggle.innerHTML = '<i class="fas fa-fill-drip"></i>';
        }
    }
}

function initializeDrawingCanvas() {
    const canvas = document.getElementById('drawing-canvas');
    if (!canvas) {
        return;
    }

    const ctx = canvas.getContext('2d');

    // Track key events for angle snapping
    document.addEventListener('keydown', (e) => { lastKeyEvent = e; });
    document.addEventListener('keyup', (e) => { lastKeyEvent = e; });

    // Instead of canvas events, use design-canvas events and draw on drawing canvas
    const designCanvas = document.getElementById('design-canvas');

    // Set up event listeners on design-canvas but draw on drawing-canvas
    designCanvas.addEventListener('mousedown', function(e) {
        if (!drawingMode || !currentDrawingTool) return;

        startDrawing(e);
    });

    designCanvas.addEventListener('mousemove', function(e) {
        if (!drawingMode || !currentDrawingTool) return;

        drawMove(e);
    });

    designCanvas.addEventListener('mouseup', function(e) {
        if (!drawingMode || !currentDrawingTool) return;

        stopDrawing(e);
    });

    // Canvas mousemove for cursor management
    designCanvas.addEventListener('mousemove', function(e) {
        if (drawingMode && currentDrawingTool) {
            return; // Let drawing mode handle cursor
        }

        // Check if hovering over a selected drawing
        const rect = designCanvas.getBoundingClientRect();
        const hoverPoint = {
            x: (e.clientX - rect.left) / zoomLevel,
            y: (e.clientY - rect.top) / zoomLevel
        };

        // Find topmost drawing at hover point
        let hoveredDrawing = null;
        for (let i = drawings.length - 1; i >= 0; i--) {
            if (isPointOnDrawing(hoverPoint, drawings[i])) {
                hoveredDrawing = drawings[i];
                break;
            }
        }

        if (hoveredDrawing) {
            // Check if this drawing is selected (single or multi)
            const isSelected = isElementSelected(hoveredDrawing, true);
            if (isSelected) {
                designCanvas.style.cursor = 'move';
                return;
            }
        }

        // Default cursor
        designCanvas.style.cursor = 'default';
    });

    // Canvas mousedown for drag initiation (like grid elements)
    designCanvas.addEventListener('mousedown', function(e) {
        if (drawingMode && currentDrawingTool) {
            return; // Let drawing mode handle this
        }

        // Check if clicking on a drawing for drag initiation
        const rect = designCanvas.getBoundingClientRect();
        const clickPoint = {
            x: (e.clientX - rect.left) / zoomLevel,
            y: (e.clientY - rect.top) / zoomLevel
        };

        // Find topmost drawing at click point (iterate from top)
        let clickedDrawing = null;
        for (let i = drawings.length - 1; i >= 0; i--) {
            if (isPointOnDrawing(clickPoint, drawings[i])) {
                clickedDrawing = drawings[i];
                break;
            }
        }

        if (clickedDrawing) {
            // If Ctrl is held, this is for multi-selection, not dragging
            if (e.ctrlKey || e.metaKey) {
                return; // Let click handler handle selection
            }

            // Check if this drawing is selected (single or multi)
            const isSelected = isElementSelected(clickedDrawing, true);

            if (isSelected) {
                // Set flag IMMEDIATELY to prevent click event interference
                isDraggingCanvasDrawing = true;

                e.preventDefault();
                e.stopPropagation();

                startDrawingDrag(e, clickedDrawing);
                return;
            }
        }
    });

    // Test event handling and drawing selection
    designCanvas.addEventListener('click', function(e) {
        if (drawingMode && currentDrawingTool) {
            // Drawing mode active
        } else if (isDraggingCanvasDrawing) {
            return; // Don't interfere with drag operation
        } else {
            // Check if clicking on a drawing for editing/selection
            const rect = designCanvas.getBoundingClientRect();
            const clickPoint = {
                x: (e.clientX - rect.left) / zoomLevel,
                y: (e.clientY - rect.top) / zoomLevel
            };

            // Always try to select drawing (multi-selection is handled inside selectDrawingAtPoint)
            selectDrawingAtPoint(clickPoint, e);
        }
    });

    // Handle double-click for canvas drawings
    designCanvas.addEventListener('dblclick', function(e) {
        if (drawingMode && currentDrawingTool) {
            return; // Don't interfere with drawing mode
        }

        const rect = designCanvas.getBoundingClientRect();
        const clickPoint = {
            x: (e.clientX - rect.left) / zoomLevel,
            y: (e.clientY - rect.top) / zoomLevel
        };

        // Find drawing at click point
        for (let i = drawings.length - 1; i >= 0; i--) {
            const drawing = drawings[i];
            if (isPointOnDrawing(clickPoint, drawing)) {
                // Handle group double-click
                handleGroupClick(drawing, true);
                break;
            }
        }
    });

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Delete key to delete selected drawing
        if (e.key === 'Delete' && selectedDrawing) {
            if (selectedDrawing.tool === 'road') {
                deleteSelectedRoad();
            } else {
                deleteSelectedDrawing();
            }
        }

        // Escape key to deselect
        if (e.key === 'Escape') {
            selectedDrawing = null;
            selectedRoad = null;
            hideRoadEditor();
            hideGeneralDrawingEditor();
            redrawCanvas();
        }
    });

    // Initial canvas setup
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';


}

function startDrawing(e) {

    if (!drawingMode || !currentDrawingTool) return;

    e.preventDefault();
    e.stopPropagation();

    isDrawing = true;
    // Get coordinates relative to design-canvas
    const designCanvas = document.getElementById('design-canvas');
    const rect = designCanvas.getBoundingClientRect();
    drawingStartPoint = {
        x: (e.clientX - rect.left) / zoomLevel,
        y: (e.clientY - rect.top) / zoomLevel
    };

    // Apply grid snapping to start point for roads
    if (currentDrawingTool === 'road') {
        console.log('Applying grid snap to start point...');
        drawingStartPoint = snapToGrid(drawingStartPoint);
    }



    if (currentDrawingTool === 'freehand') {
        // Start a new path for freehand drawing
        const newDrawing = {
            id: 'draw_' + (++drawingCounter),
            type: 'drawing',
            tool: 'freehand',
            points: [drawingStartPoint],
            style: {
                color: drawingColor,
                width: drawingWidth,
                fill: false
            }
        };
        drawings.push(newDrawing);

    }
}

function drawMove(e) {
    if (!isDrawing || !drawingMode || !currentDrawingTool) return;

    e.preventDefault();
    e.stopPropagation();

    // Get coordinates relative to design-canvas
    const designCanvas = document.getElementById('design-canvas');
    const rect = designCanvas.getBoundingClientRect();
    const currentPoint = {
        x: (e.clientX - rect.left) / zoomLevel,
        y: (e.clientY - rect.top) / zoomLevel
    };

    if (currentDrawingTool === 'freehand') {
        // Add point to current freehand drawing
        const currentDrawing = drawings[drawings.length - 1];
        currentDrawing.points.push(currentPoint);
        redrawCanvas();
    } else {
        // For other tools, show preview
        redrawCanvas();
        drawPreview(drawingStartPoint, currentPoint, e);
    }
}

function stopDrawing(e) {
    if (!isDrawing) return;



    isDrawing = false;

    if (currentDrawingTool === 'text') {
        // Handle text tool - prompt for text content
        const textContent = prompt('Enter text:');
        if (textContent && textContent.trim() !== '') {
            const newDrawing = {
                id: 'draw_' + (++drawingCounter),
                type: 'drawing',
                tool: 'text',
                points: [drawingStartPoint], // Text only needs one point
                text: textContent.trim(),
                name: textContent.trim(), // Use text content as name
                style: {
                    color: drawingColor,
                    fontSize: Math.max(drawingWidth * 2, 12), // Convert width to font size
                    fontFamily: 'Arial, sans-serif',
                    fill: true
                }
            };

            drawings.push(newDrawing);
            redrawCanvas();
            saveToHistory(`Add text: ${textContent.trim()}`);
        }
    } else if (currentDrawingTool !== 'freehand' && e.target) {
        // Complete the drawing for non-freehand tools
        const designCanvas = document.getElementById('design-canvas');
        const rect = designCanvas.getBoundingClientRect();
        let endPoint = {
            x: (e.clientX - rect.left) / zoomLevel,
            y: (e.clientY - rect.top) / zoomLevel
        };

        // Apply grid snapping for roads
        if (currentDrawingTool === 'road') {
            console.log('Applying grid snap to end point...');
            endPoint = snapToGrid(endPoint);

            // Apply angle snapping if Shift key was held during drawing
            if (lastKeyEvent && lastKeyEvent.shiftKey) {
                endPoint = snapToAngle(drawingStartPoint, endPoint);
                console.log('Applying angle snap to end point...');
            }
        }

        // Apply Shift key constraint for perfect circles
        if (currentDrawingTool === 'circle' && e.shiftKey) {
            const radiusX = Math.abs(endPoint.x - drawingStartPoint.x);
            const radiusY = Math.abs(endPoint.y - drawingStartPoint.y);
            const maxRadius = Math.max(radiusX, radiusY);

            // Adjust end point to create perfect circle
            const signX = endPoint.x >= drawingStartPoint.x ? 1 : -1;
            const signY = endPoint.y >= drawingStartPoint.y ? 1 : -1;

            endPoint = {
                x: drawingStartPoint.x + (maxRadius * signX),
                y: drawingStartPoint.y + (maxRadius * signY)
            };
        }

        const newDrawing = {
            id: 'draw_' + (++drawingCounter),
            type: 'drawing',
            tool: currentDrawingTool,
            points: [drawingStartPoint, endPoint],
            name: '', // Initialize empty name
            style: {
                color: drawingColor,
                width: drawingWidth,
                fill: drawingFill
            }
        };

        drawings.push(newDrawing);
        redrawCanvas();
        saveToHistory(`Add ${currentDrawingTool}`);
    }

    // Hide size indicator when drawing stops
    hideSizeIndicator();
}

// Simple grid snapping function
function snapToGrid(point, gridSize = 10) {
    const snapped = {
        x: Math.round(point.x / gridSize) * gridSize,
        y: Math.round(point.y / gridSize) * gridSize
    };
    console.log(`Grid snap: (${point.x.toFixed(1)}, ${point.y.toFixed(1)}) -> (${snapped.x}, ${snapped.y})`);
    return snapped;
}

// Angle snapping function for roads
function snapToAngle(startPoint, endPoint) {
    const dx = endPoint.x - startPoint.x;
    const dy = endPoint.y - startPoint.y;
    const distance = Math.sqrt(dx * dx + dy * dy);

    if (distance === 0) return endPoint;

    // Calculate current angle in degrees
    let angle = Math.atan2(dy, dx) * (180 / Math.PI);

    // Snap to nearest 15-degree increment (0°, 15°, 30°, 45°, 60°, 75°, 90°, etc.)
    const snapAngle = Math.round(angle / 15) * 15;

    // Convert back to radians
    const snapRadians = snapAngle * (Math.PI / 180);

    // Calculate new endpoint maintaining distance
    const snappedEnd = {
        x: startPoint.x + Math.cos(snapRadians) * distance,
        y: startPoint.y + Math.sin(snapRadians) * distance
    };

    console.log(`Angle snap: ${angle.toFixed(1)}° -> ${snapAngle}°`);
    return snappedEnd;
}

// Rotation angle snapping function with deadzone
function snapRotationAngle(currentAngle, snapThreshold = 5) {
    // Normalize angle to 0-360 range
    let normalizedAngle = currentAngle % 360;
    if (normalizedAngle < 0) normalizedAngle += 360;

    // Define snap angles (every 15 degrees)
    const snapAngles = [];
    for (let i = 0; i < 360; i += 15) {
        snapAngles.push(i);
    }

    // Find the closest snap angle
    let closestSnap = snapAngles[0];
    let minDistance = Math.abs(normalizedAngle - closestSnap);

    for (const snapAngle of snapAngles) {
        const distance = Math.min(
            Math.abs(normalizedAngle - snapAngle),
            Math.abs(normalizedAngle - snapAngle + 360),
            Math.abs(normalizedAngle - snapAngle - 360)
        );

        if (distance < minDistance) {
            minDistance = distance;
            closestSnap = snapAngle;
        }
    }

    // Only snap if within threshold
    if (minDistance <= snapThreshold) {
        console.log(`Rotation snap: ${normalizedAngle.toFixed(1)}° -> ${closestSnap}° (distance: ${minDistance.toFixed(1)}°)`);
        return closestSnap;
    }

    // Return original angle if not close enough to snap
    return normalizedAngle;
}

// Visual feedback for angle snapping
function showAngleSnapFeedback(angle, isSnapped) {
    let feedback = document.getElementById('angle-snap-feedback');
    if (!feedback) {
        feedback = document.createElement('div');
        feedback.id = 'angle-snap-feedback';
        feedback.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            z-index: 10000;
            pointer-events: none;
            transition: all 0.2s ease;
        `;
        document.body.appendChild(feedback);
    }

    const normalizedAngle = ((angle % 360) + 360) % 360;
    const snapIcon = isSnapped ? '🔒' : '🔄';
    const snapText = isSnapped ? 'SNAPPED' : 'SHIFT HELD';

    feedback.innerHTML = `
        ${snapIcon} ${snapText}<br>
        <strong>${normalizedAngle.toFixed(0)}°</strong><br>
        <small>Release Shift for free rotation</small>
    `;

    feedback.style.background = isSnapped ? 'rgba(40, 167, 69, 0.9)' : 'rgba(0, 123, 255, 0.9)';
    feedback.style.opacity = '1';
}

function hideAngleSnapFeedback() {
    const feedback = document.getElementById('angle-snap-feedback');
    if (feedback) {
        feedback.style.opacity = '0';
        setTimeout(() => {
            if (feedback.parentNode) {
                feedback.parentNode.removeChild(feedback);
            }
        }, 200);
    }
}

// Size indicator system
function showSizeIndicator(element, bounds, elementType = null) {
    // Don't show for excluded elements
    const excludedTypes = ['road', 'text', 'freehand'];
    const actualType = elementType || (element && element.tool) || (element && element.element_type);

    if (excludedTypes.includes(actualType)) {
        return;
    }

    // Remove existing indicator
    hideSizeIndicator();

    const indicator = document.createElement('div');
    indicator.id = 'size-indicator';
    indicator.style.cssText = `
        position: fixed;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-family: monospace;
        font-size: 12px;
        z-index: 10001;
        pointer-events: none;
        white-space: nowrap;
    `;

    // Calculate size text based on element type
    let sizeText = '';

    if (actualType === 'line') {
        // Lines show length
        const length = Math.sqrt(
            Math.pow(bounds.width, 2) + Math.pow(bounds.height, 2)
        );
        sizeText = `Length: ${Math.round(length)}px`;
    } else if (actualType === 'circle') {
        // Circles show radius and diameter
        const radius = Math.max(bounds.width, bounds.height) / 2;
        const diameter = radius * 2;
        sizeText = `⌀${Math.round(diameter)}px (r=${Math.round(radius)}px)`;
    } else {
        // Everything else shows width × height
        sizeText = `${Math.round(bounds.width)} × ${Math.round(bounds.height)}px`;
    }

    indicator.textContent = sizeText;

    // Position indicator near the element but visible
    const canvas = document.getElementById('drawing-canvas') || document.getElementById('design-canvas');
    if (canvas) {
        const canvasRect = canvas.getBoundingClientRect();
        const centerX = canvasRect.left + (bounds.centerX || bounds.x + bounds.width/2) * zoomLevel;
        const centerY = canvasRect.top + (bounds.centerY || bounds.y + bounds.height/2) * zoomLevel;

        // Position above and to the right of center
        indicator.style.left = (centerX + 20) + 'px';
        indicator.style.top = (centerY - 30) + 'px';

        // Keep indicator on screen
        const indicatorRect = indicator.getBoundingClientRect();
        if (centerX + 20 + 150 > window.innerWidth) {
            indicator.style.left = (centerX - 150) + 'px';
        }
        if (centerY - 30 < 0) {
            indicator.style.top = (centerY + 30) + 'px';
        }
    }

    document.body.appendChild(indicator);
}

function hideSizeIndicator() {
    const indicator = document.getElementById('size-indicator');
    if (indicator) {
        indicator.remove();
    }
}

// Grid snapping uses the existing 10px DOM grid

function drawPreview(startPoint, endPoint, event = null) {
    const canvas = document.getElementById('drawing-canvas');
    const ctx = canvas.getContext('2d');

    // Apply grid snapping to preview for roads
    if (currentDrawingTool === 'road') {
        console.log('Applying grid snap to preview...');
        endPoint = snapToGrid(endPoint);

        // Apply angle snapping if Shift key is held
        if (event && event.shiftKey) {
            endPoint = snapToAngle(startPoint, endPoint);
            console.log('Applying angle snap to preview...');
        }
    }

    ctx.strokeStyle = drawingColor;
    ctx.lineWidth = drawingWidth;
    ctx.fillStyle = drawingColor;

    ctx.beginPath();

    switch (currentDrawingTool) {
        case 'line':
            ctx.moveTo(startPoint.x, startPoint.y);
            ctx.lineTo(endPoint.x, endPoint.y);
            ctx.stroke();
            break;

        case 'road':
            // Road preview with grid snapping
            drawRoadPreview(ctx, startPoint, endPoint, drawingWidth);

            // Draw grid snap indicator
            ctx.save();
            ctx.strokeStyle = '#00ff00';
            ctx.lineWidth = 2;
            ctx.setLineDash([3, 3]);
            ctx.beginPath();
            ctx.arc(endPoint.x, endPoint.y, 8, 0, 2 * Math.PI);
            ctx.stroke();
            ctx.restore();
            break;

        case 'rectangle':
        case 'island':
            const width = endPoint.x - startPoint.x;
            const height = endPoint.y - startPoint.y;
            if (drawingFill) {
                ctx.fillRect(startPoint.x, startPoint.y, width, height);
            } else {
                ctx.strokeRect(startPoint.x, startPoint.y, width, height);
            }
            break;

        case 'circle':
            // Support both circle and ellipse preview based on Shift key
            let radiusX = Math.abs(endPoint.x - startPoint.x);
            let radiusY = Math.abs(endPoint.y - startPoint.y);

            // Check if Shift key is held for perfect circle constraint
            const maintainCircle = event && event.shiftKey;

            if (maintainCircle) {
                // Maintain perfect circle - use the larger radius for both axes
                const maxRadius = Math.max(radiusX, radiusY);
                radiusX = maxRadius;
                radiusY = maxRadius;

            } else {

            }

            // Draw circle or ellipse based on constraint
            ctx.ellipse(startPoint.x, startPoint.y, radiusX, radiusY, 0, 0, 2 * Math.PI);
            if (drawingFill) {
                ctx.fill();
            } else {
                ctx.stroke();
            }
            break;
    }

    // Show size indicator during drawing preview
    if (currentDrawingTool && !['road', 'text', 'freehand'].includes(currentDrawingTool)) {
        const previewBounds = {
            x: Math.min(startPoint.x, startPoint.y),
            y: Math.min(startPoint.y, endPoint.y),
            width: Math.abs(endPoint.x - startPoint.x),
            height: Math.abs(endPoint.y - startPoint.y),
            centerX: (startPoint.x + endPoint.x) / 2,
            centerY: (startPoint.y + endPoint.y) / 2
        };
        showSizeIndicator(null, previewBounds, currentDrawingTool);
    }
}

function drawRoadPreview(ctx, startPoint, endPoint, roadWidth) {
    // Calculate perpendicular offset for road edges
    const dx = endPoint.x - startPoint.x;
    const dy = endPoint.y - startPoint.y;
    const length = Math.sqrt(dx * dx + dy * dy);

    if (length === 0) return;

    const unitX = dx / length;
    const unitY = dy / length;
    const perpX = -unitY * (roadWidth / 2);
    const perpY = unitX * (roadWidth / 2);

    // Draw road outline
    ctx.strokeStyle = drawingColor;
    ctx.lineWidth = 2;

    // Top edge
    ctx.beginPath();
    ctx.moveTo(startPoint.x + perpX, startPoint.y + perpY);
    ctx.lineTo(endPoint.x + perpX, endPoint.y + perpY);
    ctx.stroke();

    // Bottom edge
    ctx.beginPath();
    ctx.moveTo(startPoint.x - perpX, startPoint.y - perpY);
    ctx.lineTo(endPoint.x - perpX, endPoint.y - perpY);
    ctx.stroke();

    // End caps
    ctx.beginPath();
    ctx.moveTo(startPoint.x + perpX, startPoint.y + perpY);
    ctx.lineTo(startPoint.x - perpX, startPoint.y - perpY);
    ctx.stroke();

    ctx.beginPath();
    ctx.moveTo(endPoint.x + perpX, endPoint.y + perpY);
    ctx.lineTo(endPoint.x - perpX, endPoint.y - perpY);
    ctx.stroke();
}

function drawRoadSegment(ctx, startPoint, endPoint, roadWidth, color, isSelected = false, roadName = '', selectionColor = '#007bff') {

    // Calculate perpendicular offset for road edges
    const dx = endPoint.x - startPoint.x;
    const dy = endPoint.y - startPoint.y;
    const length = Math.sqrt(dx * dx + dy * dy);

    if (length === 0) return;

    const unitX = dx / length;
    const unitY = dy / length;
    const perpX = -unitY * (roadWidth / 2);
    const perpY = unitX * (roadWidth / 2);



    // Draw road background (selection color fill) if selected
    if (isSelected) {
        ctx.save();
        // Convert hex color to rgba with 0.3 opacity
        const r = parseInt(selectionColor.slice(1, 3), 16);
        const g = parseInt(selectionColor.slice(3, 5), 16);
        const b = parseInt(selectionColor.slice(5, 7), 16);
        ctx.fillStyle = `rgba(${r}, ${g}, ${b}, 0.3)`;
        ctx.beginPath();
        ctx.moveTo(startPoint.x + perpX, startPoint.y + perpY);
        ctx.lineTo(endPoint.x + perpX, endPoint.y + perpY);
        ctx.lineTo(endPoint.x - perpX, endPoint.y - perpY);
        ctx.lineTo(startPoint.x - perpX, startPoint.y - perpY);
        ctx.closePath();
        ctx.fill();
        ctx.restore();
    }

    // Draw road outline
    ctx.save();
    ctx.strokeStyle = isSelected ? selectionColor : color;
    ctx.lineWidth = isSelected ? 4 : 2;

    // Top edge
    ctx.beginPath();
    ctx.moveTo(startPoint.x + perpX, startPoint.y + perpY);
    ctx.lineTo(endPoint.x + perpX, endPoint.y + perpY);
    ctx.stroke();

    // Bottom edge
    ctx.beginPath();
    ctx.moveTo(startPoint.x - perpX, startPoint.y - perpY);
    ctx.lineTo(endPoint.x - perpX, endPoint.y - perpY);
    ctx.stroke();

    // End caps
    ctx.beginPath();
    ctx.moveTo(startPoint.x + perpX, startPoint.y + perpY);
    ctx.lineTo(startPoint.x - perpX, startPoint.y - perpY);
    ctx.stroke();

    ctx.beginPath();
    ctx.moveTo(endPoint.x + perpX, endPoint.y + perpY);
    ctx.lineTo(endPoint.x - perpX, endPoint.y - perpY);
    ctx.stroke();

    ctx.restore();

    // Draw road name if provided
    if (roadName && roadName.trim() !== '') {

        // Get namePosition from the road object if available
        const road = drawings.find(d => d.points && d.points[0] === startPoint && d.points[1] === endPoint);
        const namePosition = road ? road.namePosition || 'center' : 'center';
        drawRoadName(ctx, startPoint, endPoint, roadName, roadWidth, namePosition, road);
    }
}

function drawRoadName(ctx, startPoint, endPoint, name, roadWidth, namePosition = 'center', road = null) {
    // Calculate road properties
    const dx = endPoint.x - startPoint.x;
    const dy = endPoint.y - startPoint.y;
    const length = Math.sqrt(dx * dx + dy * dy);
    const angle = Math.atan2(dy, dx);

    // Calculate position along the road based on namePosition
    let positionRatio = 0.5; // center by default
    let textAlign = 'center';

    switch (namePosition) {
        case 'left':
            positionRatio = 0.25;
            textAlign = 'center';
            break;
        case 'right':
            positionRatio = 0.75;
            textAlign = 'center';
            break;
        case 'center':
        default:
            positionRatio = 0.5;
            textAlign = 'center';
            break;
    }

    const textX = startPoint.x + (dx * positionRatio);
    const textY = startPoint.y + (dy * positionRatio);

    // Set text properties
    ctx.save();

    // Use road's text color if available, otherwise use white for good contrast
    const textColor = (road && road.textColor) ? road.textColor : '#ffffff';
    ctx.fillStyle = textColor;
    ctx.strokeStyle = textColor === '#ffffff' ? '#000000' : '#ffffff'; // Opposite color for outline
    ctx.lineWidth = 2;
    const fontSize = Math.max(Math.min(roadWidth * 0.5, 20), 12);
    ctx.font = `bold ${fontSize}px Arial`;
    ctx.textAlign = textAlign;
    ctx.textBaseline = 'middle';

    // Rotate text to match road angle
    ctx.translate(textX, textY);
    ctx.rotate(angle);

    // Draw text with outline for visibility
    ctx.strokeText(name, 0, 0);
    ctx.fillText(name, 0, 0);

    ctx.restore();
}

function redrawCanvas() {
    const canvas = document.getElementById('drawing-canvas');
    const ctx = canvas.getContext('2d');

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);



    // Redraw all drawings
    drawings.forEach(drawing => {
        // Save original styles
        ctx.save();

        // Check if drawing is selected (single or multi-selection)
        const isSingleSelected = selectedDrawing && selectedDrawing.id === drawing.id;
        const isMultiSelected = selectedDrawings.some(d => d.id === drawing.id);
        const isSelected = isSingleSelected || isMultiSelected;

        // Determine selection color to match grid elements
        let selectionColor = '#007bff'; // Blue for single selection
        if (isMultiSelected) {
            selectionColor = '#28a745'; // Green for multi-selection (matches grid elements)
        }



        // Set base styles
        ctx.strokeStyle = drawing.style.color;
        ctx.lineWidth = drawing.style.width;
        ctx.fillStyle = drawing.style.color;

        ctx.beginPath();

        switch (drawing.tool) {
            case 'freehand':
                if (drawing.points.length > 1) {

                    ctx.save();

                    // Apply rotation if it exists
                    if (drawing.rotation) {
                        const bounds = getDrawingBounds(drawing);
                        ctx.translate(bounds.centerX, bounds.centerY);
                        ctx.rotate((drawing.rotation * Math.PI) / 180);
                        ctx.translate(-bounds.centerX, -bounds.centerY);
                    }

                    // Draw selection background first
                    if (isSelected) {
                        ctx.save();
                        ctx.strokeStyle = selectionColor;
                        ctx.lineWidth = drawing.style.width + 4;
                        ctx.globalAlpha = 0.5;
                        ctx.moveTo(drawing.points[0].x, drawing.points[0].y);
                        for (let i = 1; i < drawing.points.length; i++) {
                            ctx.lineTo(drawing.points[i].x, drawing.points[i].y);
                        }
                        ctx.stroke();
                        ctx.restore();
                    }

                    // Draw the actual line
                    ctx.moveTo(drawing.points[0].x, drawing.points[0].y);
                    for (let i = 1; i < drawing.points.length; i++) {
                        ctx.lineTo(drawing.points[i].x, drawing.points[i].y);
                    }
                    ctx.stroke();

                    ctx.restore();
                }
                break;

            case 'line':
                if (drawing.points.length >= 2) {

                    ctx.save();

                    // Apply rotation if it exists
                    if (drawing.rotation) {
                        const drawingLineCenterX = (drawing.points[0].x + drawing.points[1].x) / 2;
                        const drawingLineCenterY = (drawing.points[0].y + drawing.points[1].y) / 2;
                        ctx.translate(drawingLineCenterX, drawingLineCenterY);
                        ctx.rotate((drawing.rotation * Math.PI) / 180);
                        ctx.translate(-drawingLineCenterX, -drawingLineCenterY);
                    }

                    // Draw selection background first
                    if (isSelected) {
                        ctx.save();
                        ctx.strokeStyle = selectionColor;
                        ctx.lineWidth = drawing.style.width + 4;
                        ctx.globalAlpha = 0.5;
                        ctx.moveTo(drawing.points[0].x, drawing.points[0].y);
                        ctx.lineTo(drawing.points[1].x, drawing.points[1].y);
                        ctx.stroke();
                        ctx.restore();
                    }

                    // Draw the actual line
                    ctx.moveTo(drawing.points[0].x, drawing.points[0].y);
                    ctx.lineTo(drawing.points[1].x, drawing.points[1].y);
                    ctx.stroke();

                    ctx.restore();
                }
                break;

            case 'road':
                if (drawing.points.length >= 2) {
                    const isSelected = (selectedRoad && selectedRoad.id === drawing.id) || (selectedDrawing && selectedDrawing.id === drawing.id);
                    const roadName = drawing.name || '';

                    ctx.save();

                    // Apply rotation if it exists
                    if (drawing.rotation) {
                        const drawingRoadCenterX = (drawing.points[0].x + drawing.points[1].x) / 2;
                        const drawingRoadCenterY = (drawing.points[0].y + drawing.points[1].y) / 2;
                        ctx.translate(drawingRoadCenterX, drawingRoadCenterY);
                        ctx.rotate((drawing.rotation * Math.PI) / 180);
                        ctx.translate(-drawingRoadCenterX, -drawingRoadCenterY);
                    }

                    // Draw road as solid black rectangle
                    drawSolidRoad(ctx, drawing, isSelected, roadName, selectionColor);



                    ctx.restore();
                }
                break;

            case 'rectangle':
            case 'island':
                if (drawing.points.length >= 2) {
                    const width = drawing.points[1].x - drawing.points[0].x;
                    const height = drawing.points[1].y - drawing.points[0].y;
                    const drawingRectCenterX = drawing.points[0].x + width / 2;
                    const drawingRectCenterY = drawing.points[0].y + height / 2;



                    ctx.save();

                    // Apply rotation if it exists
                    if (drawing.rotation) {
                        ctx.translate(drawingRectCenterX, drawingRectCenterY);
                        ctx.rotate((drawing.rotation * Math.PI) / 180);
                        ctx.translate(-drawingRectCenterX, -drawingRectCenterY);
                    }

                    // Draw selection background first
                    if (isSelected) {
                        // Convert hex color to rgba with 0.3 opacity
                        const r = parseInt(selectionColor.slice(1, 3), 16);
                        const g = parseInt(selectionColor.slice(3, 5), 16);
                        const b = parseInt(selectionColor.slice(5, 7), 16);
                        ctx.fillStyle = `rgba(${r}, ${g}, ${b}, 0.3)`;
                        ctx.fillRect(drawing.points[0].x - 3, drawing.points[0].y - 3, width + 6, height + 6);
                        ctx.strokeStyle = selectionColor;
                        ctx.lineWidth = 4;
                        ctx.strokeRect(drawing.points[0].x - 3, drawing.points[0].y - 3, width + 6, height + 6);
                    }

                    // Draw the actual shape
                    if (drawing.style.fill) {
                        ctx.fillRect(drawing.points[0].x, drawing.points[0].y, width, height);
                    }
                    ctx.strokeRect(drawing.points[0].x, drawing.points[0].y, width, height);

                    ctx.restore();
                }
                break;

            case 'circle':
                if (drawing.points.length >= 2) {
                    // Calculate ellipse radii from center and edge points
                    const centerX = drawing.points[0].x;
                    const centerY = drawing.points[0].y;
                    const edgeX = drawing.points[1].x;
                    const edgeY = drawing.points[1].y;

                    // Calculate radii (edge point represents the corner of bounding box)
                    const radiusX = Math.abs(edgeX - centerX);
                    const radiusY = Math.abs(edgeY - centerY);



                    ctx.save();

                    // Apply rotation if it exists
                    if (drawing.rotation) {
                        ctx.translate(centerX, centerY);
                        ctx.rotate((drawing.rotation * Math.PI) / 180);
                        ctx.translate(-centerX, -centerY);
                    }

                    // Draw selection background first
                    if (isSelected) {
                        ctx.save();
                        // Convert hex color to rgba with 0.3 opacity
                        const r = parseInt(selectionColor.slice(1, 3), 16);
                        const g = parseInt(selectionColor.slice(3, 5), 16);
                        const b = parseInt(selectionColor.slice(5, 7), 16);
                        ctx.fillStyle = `rgba(${r}, ${g}, ${b}, 0.3)`;
                        ctx.beginPath();
                        ctx.ellipse(centerX, centerY, radiusX + 5, radiusY + 5, 0, 0, 2 * Math.PI);
                        ctx.fill();
                        ctx.strokeStyle = selectionColor;
                        ctx.lineWidth = 4;
                        ctx.stroke();
                        ctx.restore();
                    }

                    // Draw the actual circle/ellipse
                    ctx.beginPath();
                    ctx.ellipse(centerX, centerY, radiusX, radiusY, 0, 0, 2 * Math.PI);

                    if (drawing.style.fill) {
                        ctx.fill();
                    }
                    ctx.stroke();

                    ctx.restore();
                }
                break;

            case 'text':
                if (drawing.points.length >= 1 && drawing.text) {
                    const textX = drawing.points[0].x;
                    const textY = drawing.points[0].y;

                    // Compute text metrics first to get center
                    const fontSize = drawing.style.fontSize || 16;
                    const fontFamily = drawing.style.fontFamily || 'Arial, sans-serif';

                    ctx.save();
                    ctx.font = `${fontSize}px ${fontFamily}`;
                    const textMetrics = ctx.measureText(drawing.text);
                    const textWidth = textMetrics.width;
                    const textHeight = fontSize;

                    // Compute center of the text rect (top-left is stored)
                    const centerX = textX + textWidth / 2;
                    const centerY = textY + textHeight / 2;

                    // Rotate around center so handles, hit-tests, and drawing agree
                    ctx.translate(centerX, centerY);
                    if (drawing.rotation) {
                        ctx.rotate((drawing.rotation * Math.PI) / 180);
                    }

                    // Draw with center alignment at (0,0)
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';

                    // Draw selection background first (centered box)
                    if (isSelected) {
                        ctx.save();

                        // Convert hex color to rgba with 0.3 opacity
                        const r = parseInt(selectionColor.slice(1, 3), 16);
                        const g = parseInt(selectionColor.slice(3, 5), 16);
                        const b = parseInt(selectionColor.slice(5, 7), 16);
                        ctx.fillStyle = `rgba(${r}, ${g}, ${b}, 0.3)`;
                        ctx.fillRect(-textWidth / 2 - 3, -textHeight / 2 - 3, textWidth + 6, textHeight + 6);

                        ctx.strokeStyle = selectionColor;
                        ctx.lineWidth = 2;
                        ctx.strokeRect(-textWidth / 2 - 3, -textHeight / 2 - 3, textWidth + 6, textHeight + 6);
                        ctx.restore();
                    }

                    // Draw the actual text at the rotated center
                    ctx.fillStyle = drawing.style.color;
                    ctx.fillText(drawing.text, 0, 0);

                    ctx.restore();
                }
                break;
        }

        // Restore context for each drawing
        ctx.restore();
    });
}











// Draw road as solid black rectangle with white text
function drawSolidRoad(ctx, road, isSelected, roadName, selectionColor) {
    const startPoint = road.points[0];
    const endPoint = road.points[1];
    const roadWidth = road.style.width;

    // Calculate road rectangle corners
    const dx = endPoint.x - startPoint.x;
    const dy = endPoint.y - startPoint.y;
    const length = Math.sqrt(dx * dx + dy * dy);

    if (length === 0) return;

    const unitX = dx / length;
    const unitY = dy / length;
    const perpX = -unitY * (roadWidth / 2);
    const perpY = unitX * (roadWidth / 2);

    // Calculate the four corners of the road rectangle
    const corners = [
        { x: startPoint.x + perpX, y: startPoint.y + perpY }, // top-left
        { x: endPoint.x + perpX, y: endPoint.y + perpY },     // top-right
        { x: endPoint.x - perpX, y: endPoint.y - perpY },     // bottom-right
        { x: startPoint.x - perpX, y: startPoint.y - perpY }  // bottom-left
    ];

    // Draw solid road with the road's color
    ctx.fillStyle = road.style.color || 'black';
    ctx.beginPath();
    ctx.moveTo(corners[0].x, corners[0].y);
    ctx.lineTo(corners[1].x, corners[1].y);
    ctx.lineTo(corners[2].x, corners[2].y);
    ctx.lineTo(corners[3].x, corners[3].y);
    ctx.closePath();
    ctx.fill();

    // Draw selection outline if selected
    if (isSelected) {
        ctx.strokeStyle = selectionColor;
        ctx.lineWidth = 2;
        ctx.stroke();
    }

    // Draw road name if it exists
    if (roadName && roadName.trim() !== '') {
        const namePosition = road.namePosition || 'center';
        drawRoadName(ctx, startPoint, endPoint, roadName, roadWidth, namePosition, road);
    }
}



function fixElementColors() {
    layoutElements.forEach(element => {
        // Fix missing, white, or transparent colors
        if (!element.color || element.color === '#ffffff' || element.color === '#fff' || element.color === 'white' || element.color === '') {
            element.color = getDefaultColor(element.element_type);
        }

        // Ensure color property exists and is properly formatted
        if (!element.color.startsWith('#')) {
            element.color = getDefaultColor(element.element_type);
        }
    });
}

function setupElementListListeners() {
    // Type filter dropdown
    const typeFilter = document.getElementById('type-filter');
    if (typeFilter) {
        typeFilter.addEventListener('change', filterByType);
    }

    // Delete selected button
    const deleteSelectedBtn = document.getElementById('delete-selected');
    if (deleteSelectedBtn) {
        deleteSelectedBtn.addEventListener('click', deleteSelectedElements);
    }

    // Select all checkbox
    const selectAllCheckbox = document.getElementById('select-all');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', toggleSelectAll);
    }

    // Element checkboxes (delegated event listener)
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('element-checkbox')) {
            updateDeleteSelectedButton();
        }
    });
}



function addElement(elementType) {
    elementCounter++;

    const element = {
        id: 'new_' + elementCounter,
        layout_id: layoutId, // Add the layout_id property
        element_type: elementType,
        name: getDefaultName(elementType),
        x_position: 100 + (elementCounter * 20),
        y_position: 100 + (elementCounter * 20),
        width: getDefaultWidth(elementType),
        height: getDefaultHeight(elementType),
        rotation: 0,
        color: getDefaultColor(elementType),
        border_color: '#dee2e6',
        opacity: 1.0,
        z_index: 1,
        properties: getDefaultProperties(elementType),
        category_id: null,
        capacity: getDefaultCapacity(elementType),
        handicap_accessible: elementType === 'visitor_parking' ? 0 : null
    };

    layoutElements.push(element);
    saveToHistory(`Add ${getDefaultName(elementType)}`);
    renderElements();
    updateStats();
    updateElementList();
    selectElement(element);
}

function getDefaultName(elementType) {
    const names = {
        'show_parking': 'Show Parking',
        'visitor_parking': 'Visitor Parking',
        'building': 'Building',
        'vendor_area': 'Vendor Area',
        'vendor_booth': 'Vendor Booth',
        'entrance': 'Entrance',
        'exit': 'Exit',
        'restroom': 'Restroom',
        'food_area': 'Food Area',
        'stage': 'Stage',
        'judging_area': 'Judging Area',
        'display_area': 'Display Area',
        'kids_area': 'Kids Area',
        'registration_area': 'Registration/Check-in',
        'emergency': 'Emergency Access'
    };
    return names[elementType] || 'Element';
}

function getDefaultWidth(elementType) {
    const widths = {
        'show_parking': 300,
        'visitor_parking': 250,
        'building': 200,
        'vendor_area': 400,
        'vendor_booth': 100,
        'entrance': 100,
        'exit': 100,
        'restroom': 80,
        'food_area': 200,
        'stage': 250,
        'judging_area': 200,
        'display_area': 150,
        'kids_area': 200,
        'registration_area': 150,
        'emergency': 120
    };
    return widths[elementType] || 150;
}

function getDefaultHeight(elementType) {
    const heights = {
        'show_parking': 200,
        'visitor_parking': 150,
        'building': 120,
        'vendor_area': 100,
        'vendor_booth': 100,
        'entrance': 60,
        'exit': 60,
        'restroom': 60,
        'food_area': 150,
        'stage': 100,
        'judging_area': 150,
        'display_area': 100,
        'kids_area': 150,
        'registration_area': 100,
        'emergency': 80
    };
    return heights[elementType] || 100;
}

function getDefaultColor(elementType) {
    const colors = {
        'show_parking': '#007bff',
        'visitor_parking': '#17a2b8',
        'building': '#6c757d',
        'vendor_area': '#ffc107',
        'vendor_booth': '#ffc107',
        'entrance': '#28a745',
        'exit': '#dc3545',
        'restroom': '#6c757d',
        'food_area': '#fd7e14',
        'stage': '#17a2b8',
        'judging_area': '#28a745',
        'display_area': '#17a2b8',
        'kids_area': '#e83e8c',
        'registration_area': '#6f42c1',
        'emergency': '#dc3545'
    };
    return colors[elementType] || '#007bff';
}

function getDefaultCapacity(elementType) {
    const capacities = {
        'show_parking': 50,
        'visitor_parking': 30,
        'vendor_area': 10,
        'vendor_booth': 1
    };
    return capacities[elementType] || null;
}

function getDefaultProperties(elementType) {
    const properties = {
        'show_parking': { category: null, vehicle_capacity: 50 },
        'visitor_parking': { vehicle_capacity: 30, handicap_accessible: false },
        'building': { description: 'Building', floors: 1 },
        'vendor_area': { booth_count: 10, booth_size: '10x10' },
        'vendor_booth': { booth_size: '10x10', power: false, table: false },
        'entrance': { capacity: null, description: 'Main entrance' },
        'exit': { capacity: null, description: 'Exit point' },
        'restroom': { facilities: ['mens', 'womens'] },
        'food_area': { vendor_count: 3 },
        'stage': { description: 'Performance area' },
        'judging_area': { description: 'Vehicle judging area' },
        'display_area': { description: 'Special display area' },
        'emergency': { description: 'Emergency vehicle access' }
    };
    return properties[elementType] || {};
}

function renderElements() {
    const canvas = document.getElementById('design-canvas');

    // Remove all resize handles first
    removeAllResizeHandles();

    // Remove existing element divs
    const existingElements = canvas.querySelectorAll('.layout-element');
    existingElements.forEach(el => el.remove());

    // Render each element
    layoutElements.forEach(element => {
        const div = document.createElement('div');
        div.className = 'layout-element ' + getElementClass(element.element_type);

        // TEMPORARILY DISABLED: Group indicators
        // const elementGroup = findElementGroup(element);
        // if (elementGroup) {
        //     div.classList.add('grouped');
        //     div.title = `Part of group: ${elementGroup.name}`;
        // }

        div.id = 'element_' + element.id;
        div.style.left = element.x_position + 'px';
        div.style.top = element.y_position + 'px';
        div.style.width = element.width + 'px';
        div.style.height = element.height + 'px';
        div.style.backgroundColor = element.color + '20'; // Add transparency
        div.style.borderColor = element.color;
        div.style.color = element.color;
        div.style.opacity = element.opacity;
        div.style.zIndex = element.z_index;
        div.style.transform = `rotate(${element.rotation}deg)`;

        // Element content
        let displayName = element.name;
        let additionalInfo = element.capacity ? `<small>${element.capacity} capacity</small>` : '';

        // Handle different element types
        if (element.element_type === 'show_parking') {
            if (element.category_id) {
                const category = categories.find(cat => cat.id == element.category_id);
                if (category) {
                    const categoryPrefix = getCategoryPrefix(category.name);
                    displayName = `<div style="font-weight: bold; line-height: 1.2;">${category.name}</div><div style="font-size: 0.9em; color: #666; margin-top: 2px;">&lt;${categoryPrefix}&gt;</div>`;
                }
            }
            additionalInfo = `<small>${element.capacity || 50} spaces</small>`;
        } else if (element.element_type === 'visitor_parking') {
            additionalInfo = `<small>${element.capacity || 30} spaces</small>`;
            // Check if handicap accessible
            const isHandicap = element.handicap_accessible || (element.properties && element.properties.handicap_accessible);
            if (isHandicap) {
                div.classList.add('handicap');
                // Change display name to show handicap accessibility symbol and "Parking" below it
                displayName = `<div style="font-size: 1.5em; line-height: 1; margin-bottom: 4px;"><i class="fas fa-wheelchair" style="color: #28a745;"></i></div><div style="font-size: 1em; font-weight: bold;">Parking</div>`;
            } else {
                // Show "Visitor Parking" when not handicap accessible
                displayName = 'Visitor Parking';
            }
        } else if (element.element_type === 'kids_area') {
            const kidsAreaType = element.properties && element.properties.kids_area_type || 'inflatables';
            const ageRange = element.properties && element.properties.age_range || 'All Ages';
            const typeDisplayMap = {
                'inflatables': 'Inflatables',
                'playground': 'Playground',
                'face_painting': 'Face Painting',
                'misc_craft': 'Crafts',
                'coloring': 'Coloring',
                'rest_area': 'Rest Area',
                'games': 'Games',
                'story_time': 'Story Time',
                'other': 'Kids Area'
            };
            displayName = `<div style="font-weight: bold; line-height: 1.2;">${typeDisplayMap[kidsAreaType]}</div><div style="font-size: 0.8em; color: #666; margin-top: 1px;">${ageRange}</div>`;
            additionalInfo = `<small>${element.capacity || 20} kids</small>`;
        } else if (element.element_type === 'registration_area') {
            const registrationType = element.properties && element.properties.registration_type || 'general';
            const typeDisplayMap = {
                'general': 'Registration',
                'show_participants': 'Show Check-in',
                'vendors': 'Vendor Check-in',
                'vip': 'VIP Check-in',
                'staff': 'Staff Check-in',
                'judges': 'Judge Check-in'
            };
            displayName = `<div style="font-weight: bold; line-height: 1.2;">${typeDisplayMap[registrationType]}</div>`;
            additionalInfo = `<small>${element.capacity || 2} staff</small>`;
        }

        // Calculate responsive font size - only scale down when box is actually too small
        const minDimension = Math.min(element.width, element.height);
        let fontSize = '14px';  // Keep original good size as default

        // Only scale down when element is very small (barely any visible space at edge)
        if (minDimension < 50) {
            fontSize = '9px';
        } else if (minDimension < 70) {
            fontSize = '11px';
        }
        // For elements 70px and larger, keep the original 14px size that was working fine

        div.innerHTML = `
            <div class="text-center" style="font-size: ${fontSize}; line-height: 1.2; padding: 4px;">
                <div class="fw-bold">${displayName}</div>
                ${additionalInfo}
            </div>
        `;

        // Add resize handles if this element is selected
        if (selectedElement && selectedElement.id === element.id) {
            addResizeHandles(div, element);
        }

        // Add event listeners with click delay to prevent double-firing
        let clickTimeout = null;

        div.addEventListener('click', function(e) {
            e.stopPropagation();


            // Clear any existing timeout
            if (clickTimeout) {
                clearTimeout(clickTimeout);
            }

            // Delay click handling to avoid conflict with double-click
            clickTimeout = setTimeout(() => {

                selectElement(element, false, false, e);
                clickTimeout = null;
            }, 200); // 200ms delay
        });

        div.addEventListener('dblclick', function(e) {
            e.stopPropagation();


            // Clear the click timeout since this is a double-click
            if (clickTimeout) {
                clearTimeout(clickTimeout);
                clickTimeout = null;
            }

            selectElement(element, true, false, e); // Show properties on double click
        });

        div.addEventListener('mousedown', function(e) {
            // Only start drag, don't select again (click event handles selection)
            startDrag(e, element);
        });

        // Add right-click context menu for overlapping elements
        div.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            e.stopPropagation();
            showOverlappingElementsMenu(e, element);
        });

        canvas.appendChild(div);
    });

    // Restore selection styling after recreating elements
    // This ensures multi-selected elements keep their green borders during drag
    selectedElements.forEach(element => {
        const div = document.getElementById('element_' + element.id);
        if (div) {
            div.classList.add('multi-selected');
        }
    });

    // Style single selected element (only if no multi-selection)
    if (selectedElement && selectedElements.length === 0) {
        const div = document.getElementById('element_' + selectedElement.id);
        if (div) {
            div.classList.add('selected');
        }
    }
}

function getElementClass(elementType) {
    const classes = {
        'show_parking': 'show-parking',
        'visitor_parking': 'visitor-parking',
        'building': 'building',
        'vendor_area': 'vendor-area',
        'vendor_booth': 'vendor-booth',
        'entrance': 'facility',
        'exit': 'facility',
        'restroom': 'facility',
        'food_area': 'facility',
        'stage': 'special-area',
        'judging_area': 'special-area',
        'display_area': 'special-area',
        'emergency': 'facility'
    };
    return classes[elementType] || 'facility';
}

function getElementTypeDisplay(elementType) {
    const displayNames = {
        'show_parking': 'Show Parking',
        'visitor_parking': 'Visitor Parking',
        'building': 'Building',
        'vendor_area': 'Vendor Area',
        'vendor_booth': 'Vendor Booth',
        'entrance': 'Entrance',
        'exit': 'Exit',
        'restroom': 'Restroom',
        'food_area': 'Food Area',
        'stage': 'Stage',
        'judging_area': 'Judging Area',
        'display_area': 'Display Area',
        'emergency': 'Emergency'
    };
    return displayNames[elementType] || elementType.replace('_', ' ');
}

function selectElement(element, showProperties = false, multiSelect = false, clickEvent = null) {
    // Prevent double-processing
    if (isProcessingSelection) {
        return;
    }

    isProcessingSelection = true;

    // Check if Ctrl key is being held for multi-selection
    const currentEvent = clickEvent || window.event;
    const isCtrlHeld = currentEvent && (currentEvent.ctrlKey || currentEvent.metaKey);
    const shouldMultiSelect = multiSelect || isCtrlHeld;

    // Check if element is part of a group and handle group selection
    if (element) {
        const elementGroup = findElementGroup(element);
        if (elementGroup) {
            if (shouldMultiSelect) {
                // Ctrl+Click: Add entire group to existing selection
                selectEntireGroupMulti(elementGroup);
            } else {
                // Normal click: Select only this group
                selectEntireGroup(elementGroup);
            }

            // Handle group highlighting in group manager
            const isDoubleClick = currentEvent && currentEvent.detail === 2;
            handleGroupClick(element, isDoubleClick);

            // Reset processing flag and return
            setTimeout(() => {
                isProcessingSelection = false;
            }, 100);
            return;
        }
    }

    if (element) {
        if (shouldMultiSelect) {
            // Multi-selection mode
            // If this is the first Ctrl+click and we have a single selection, add it to multi-selection first
            if (selectedElement && selectedElements.length === 0 && selectedElement !== element) {
                selectedElements.push(selectedElement);
                selectedElement = null; // Clear single selection
                removeAllResizeHandles(); // Remove single selection handles
            }

            if (isElementSelected(element, false)) {
                // Element is already selected
                // Only remove it if there are multiple items selected
                // If it's the only selected item, keep it selected
                const totalSelected = selectedElements.length + (selectedElement ? 1 : 0);
                if (totalSelected > 1) {
                    removeFromSelection(element, false);
                }
                // If only one item selected, do nothing (keep it selected)
            } else {
                // Add to selection - this works even if starting fresh with Ctrl+Click
                addToSelection(element, false);

            }
        } else {
            // Single selection mode - clear all previous selections

            clearSelection();
            removeAllResizeHandles();

            selectedElement = element;

            // Add resize handles to selected element
            const elementDiv = document.getElementById('element_' + element.id);
            if (elementDiv) {
                addResizeHandles(elementDiv, element);

                // Show size indicator for selected grid element
                const bounds = {
                    x: element.x_position,
                    y: element.y_position,
                    width: element.width,
                    height: element.height,
                    centerX: element.x_position + element.width / 2,
                    centerY: element.y_position + element.height / 2
                };
                showSizeIndicator(element, bounds, element.element_type);
            }
        }

        // Show properties panel only if requested and single selection
        if (showProperties && !shouldMultiSelect) {
            showElementProperties(element);
        }

        updateSelectionDisplay();
    } else {
        // Deselect all
        clearSelection();
        removeAllResizeHandles();
        document.getElementById('element-properties').style.display = 'none';
    }

    // Reset processing flag after a short delay
    setTimeout(() => {
        isProcessingSelection = false;
    }, 100);
}

function removeAllResizeHandles() {
    // Remove ALL resize handles from the entire canvas
    document.querySelectorAll('.resize-handle').forEach(handle => handle.remove());
    // Also remove rotation handles
    document.querySelectorAll('.rotation-handle').forEach(handle => handle.remove());
}

function showElementProperties(element) {
    const panel = document.getElementById('element-properties');
    const content = document.getElementById('properties-content');

    let html = `
        <div class="mb-3">
            <label class="form-label">Name</label>
            <input type="text" class="form-control form-control-sm" value="${element.name}"
                   onchange="updateElementProperty('name', this.value)">
        </div>

        <div class="row mb-3">
            <div class="col-6">
                <label class="form-label">Width</label>
                <input type="number" class="form-control form-control-sm" value="${element.width}"
                       onchange="updateElementProperty('width', parseInt(this.value))">
            </div>
            <div class="col-6">
                <label class="form-label">Height</label>
                <input type="number" class="form-control form-control-sm" value="${element.height}"
                       onchange="updateElementProperty('height', parseInt(this.value))">
            </div>
        </div>

        <div class="mb-3">
            <label class="form-label">Color</label>
            <input type="color" class="form-control form-control-color" value="${element.color}"
                   onchange="updateElementProperty('color', this.value)">
        </div>
    `;

    // Add element-specific properties
    if (element.element_type === 'show_parking') {
        html += `
            <div class="mb-3">
                <label class="form-label">Category</label>
                <select class="form-select form-select-sm" onchange="updateElementProperty('category_id', this.value || null)">
                    <option value="">No category assigned</option>
                    ${categories.map(cat => `
                        <option value="${cat.id}" ${element.category_id == cat.id ? 'selected' : ''}>
                            ${cat.name}
                        </option>
                    `).join('')}
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Vehicle Capacity</label>
                <input type="number" class="form-control form-control-sm" value="${element.capacity || 50}"
                       onchange="updateElementProperty('capacity', parseInt(this.value))">
            </div>
        `;
    } else if (element.element_type === 'visitor_parking') {
        const isHandicap = element.handicap_accessible || (element.properties && element.properties.handicap_accessible);
        html += `
            <div class="mb-3">
                <label class="form-label">Vehicle Capacity</label>
                <input type="number" class="form-control form-control-sm" value="${element.capacity || 30}"
                       onchange="updateElementProperty('capacity', parseInt(this.value))">
            </div>
            <div class="mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="handicap-check" ${isHandicap ? 'checked' : ''}
                           onchange="updateHandicapAccessible(this.checked)">
                    <label class="form-check-label" for="handicap-check">
                        Handicap Accessible
                    </label>
                </div>
            </div>
        `;
    } else if (element.element_type === 'building') {
        html += `
            <div class="mb-3">
                <label class="form-label">Description</label>
                <input type="text" class="form-control form-control-sm"
                       value="${element.properties && element.properties.description || 'Building'}"
                       onchange="updateElementPropertyInProperties('description', this.value)">
            </div>
        `;
    } else if (element.element_type === 'vendor_area') {
        html += `
            <div class="mb-3">
                <label class="form-label">Booth Count</label>
                <input type="number" class="form-control form-control-sm" value="${element.capacity || 10}"
                       onchange="updateElementProperty('capacity', parseInt(this.value))">
            </div>
        `;
    } else if (element.element_type === 'kids_area') {
        console.log('Showing kids area properties for element:', element);
        const kidsAreaType = element.properties && element.properties.kids_area_type || 'inflatables';
        html += `
            <div class="mb-3">
                <label class="form-label">Kids Area Type</label>
                <select class="form-select form-select-sm" onchange="updateElementPropertyInProperties('kids_area_type', this.value)">
                    <option value="inflatables" ${kidsAreaType === 'inflatables' ? 'selected' : ''}>Inflatables</option>
                    <option value="playground" ${kidsAreaType === 'playground' ? 'selected' : ''}>Playground</option>
                    <option value="face_painting" ${kidsAreaType === 'face_painting' ? 'selected' : ''}>Face Painting</option>
                    <option value="misc_craft" ${kidsAreaType === 'misc_craft' ? 'selected' : ''}>Misc Craft</option>
                    <option value="coloring" ${kidsAreaType === 'coloring' ? 'selected' : ''}>Coloring</option>
                    <option value="rest_area" ${kidsAreaType === 'rest_area' ? 'selected' : ''}>Rest Area</option>
                    <option value="games" ${kidsAreaType === 'games' ? 'selected' : ''}>Games</option>
                    <option value="story_time" ${kidsAreaType === 'story_time' ? 'selected' : ''}>Story Time</option>
                    <option value="other" ${kidsAreaType === 'other' ? 'selected' : ''}>Other</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Age Range</label>
                <input type="text" class="form-control form-control-sm"
                       value="${element.properties && element.properties.age_range || 'All Ages'}"
                       placeholder="e.g., 3-12 years"
                       onchange="updateElementPropertyInProperties('age_range', this.value)">
            </div>
            <div class="mb-3">
                <label class="form-label">Capacity</label>
                <input type="number" class="form-control form-control-sm" value="${element.capacity || 20}"
                       onchange="updateElementProperty('capacity', parseInt(this.value))">
            </div>
        `;
    } else if (element.element_type === 'registration_area') {
        const registrationType = element.properties && element.properties.registration_type || 'general';
        html += `
            <div class="mb-3">
                <label class="form-label">Registration Type</label>
                <select class="form-select form-select-sm" onchange="updateElementPropertyInProperties('registration_type', this.value)">
                    <option value="general" ${registrationType === 'general' ? 'selected' : ''}>General Registration</option>
                    <option value="show_participants" ${registrationType === 'show_participants' ? 'selected' : ''}>Show Participants</option>
                    <option value="vendors" ${registrationType === 'vendors' ? 'selected' : ''}>Vendor Check-in</option>
                    <option value="vip" ${registrationType === 'vip' ? 'selected' : ''}>VIP/Special Guests</option>
                    <option value="staff" ${registrationType === 'staff' ? 'selected' : ''}>Staff Check-in</option>
                    <option value="judges" ${registrationType === 'judges' ? 'selected' : ''}>Judge Check-in</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Staff Count</label>
                <input type="number" class="form-control form-control-sm" value="${element.capacity || 2}"
                       onchange="updateElementProperty('capacity', parseInt(this.value))">
            </div>
        `;
    }

    html += `
        <div class="mt-3 pt-3 border-top">
            <button class="btn btn-sm btn-danger w-100" onclick="deleteElement()">
                <i class="fas fa-trash"></i> Delete Element
            </button>
        </div>
    `;

    content.innerHTML = html;

    // Only reset panel position to center if it's currently hidden
    if (panel.style.display === 'none' || !panel.style.display) {
        panel.style.left = '';
        panel.style.top = '';
        panel.style.transform = 'translate(-50%, -50%)';
    }

    panel.style.display = 'block';
}

function updateElementProperty(property, value) {
    if (!selectedElement) return;

    const oldValue = selectedElement[property];
    selectedElement[property] = value;

    // Only save to history if value actually changed
    if (oldValue !== value) {
        saveToHistory(`Change ${property} of ${selectedElement.name}`);
    }

    // Update group data if this element was selected from group manager
    if (selectedFromGroupManager) {
        // Update the original data to reflect the change
        selectedFromGroupManager.originalData[property] = value;
    }

    // Update displays to reflect the change
    renderElements();
    updateStats();
    updateElementList();
}

function updateHandicapAccessible(isHandicap) {
    if (!selectedElement) return;

    selectedElement.handicap_accessible = isHandicap ? 1 : 0;

    // Also update in properties for consistency
    if (!selectedElement.properties) {
        selectedElement.properties = {};
    }
    selectedElement.properties.handicap_accessible = isHandicap;

    saveToHistory(`${isHandicap ? 'Enable' : 'Disable'} handicap accessibility for ${selectedElement.name}`);

    // Update displays to reflect the change
    renderElements();
    updateStats();
    updateElementList();
}

function updateElementPropertyInProperties(property, value) {
    if (!selectedElement) return;

    if (!selectedElement.properties) {
        selectedElement.properties = {};
    }

    const oldValue = selectedElement.properties[property];
    selectedElement.properties[property] = value;

    // Only save to history if value actually changed
    if (oldValue !== value) {
        saveToHistory(`Change ${property} of ${selectedElement.name}`);
    }

    // Update displays to reflect the change
    renderElements();
    updateStats();
    updateElementList();
}

function showOverlappingElementsMenu(event, clickedElement) {
    // Remove any existing context menu
    const existingMenu = document.querySelector('.context-menu');
    if (existingMenu) {
        existingMenu.remove();
    }

    // Get all elements at the clicked position
    const clickX = event.clientX;
    const clickY = event.clientY;
    const canvas = document.getElementById('design-canvas');
    const canvasRect = canvas.getBoundingClientRect();

    // Convert screen coordinates to canvas coordinates
    const canvasX = (clickX - canvasRect.left) / zoomLevel;
    const canvasY = (clickY - canvasRect.top) / zoomLevel;

    // Find all elements that contain this point
    const overlappingElements = layoutElements.filter(element => {
        return canvasX >= element.x_position &&
               canvasX <= element.x_position + element.width &&
               canvasY >= element.y_position &&
               canvasY <= element.y_position + element.height;
    });

    // Sort by z-index (highest first) so top elements appear first in menu
    overlappingElements.sort((a, b) => (b.z_index || 1) - (a.z_index || 1));

    if (overlappingElements.length <= 1) {
        // No overlapping elements, just select the clicked element normally
        selectElement(clickedElement, false);
        return;
    }

    // Create context menu
    const menu = document.createElement('div');
    menu.className = 'context-menu';

    // Use proper viewport positioning accounting for scroll
    const menuX = clickX + window.pageXOffset;
    const menuY = clickY + window.pageYOffset;

    menu.style.position = 'absolute';
    menu.style.left = menuX + 'px';
    menu.style.top = menuY + 'px';

    // Add header
    const header = document.createElement('div');
    header.className = 'context-menu-item';
    header.style.fontWeight = 'bold';
    header.style.backgroundColor = '#f8f9fa';
    header.style.cursor = 'default';
    header.textContent = `${overlappingElements.length} overlapping elements:`;
    menu.appendChild(header);

    // Add menu items for each overlapping element
    overlappingElements.forEach((element, index) => {
        const item = document.createElement('div');
        item.className = 'context-menu-item';

        // Create type indicator
        const indicator = document.createElement('div');
        indicator.className = 'element-type-indicator';
        indicator.style.backgroundColor = element.color || '#007bff';

        // Create text content
        const text = document.createElement('span');
        text.textContent = `${element.name} (${getElementTypeDisplay(element.element_type)})`;
        if (index === 0) {
            text.textContent += ' - Top';
        }

        item.appendChild(indicator);
        item.appendChild(text);

        // Add click handler
        item.addEventListener('click', () => {
            selectElement(element, false);
            menu.remove();
        });

        // Add double-click handler for properties
        item.addEventListener('dblclick', () => {
            selectElement(element, true);
            menu.remove();
        });

        menu.appendChild(item);
    });

    // Add to document
    document.body.appendChild(menu);

    // Position menu to stay within viewport
    const menuRect = menu.getBoundingClientRect();

    if (menuRect.right > window.innerWidth) {
        menu.style.left = (menuX - menuRect.width) + 'px';
    }
    if (menuRect.bottom > window.innerHeight) {
        menu.style.top = (menuY - menuRect.height) + 'px';
    }

    // Close menu when clicking elsewhere
    const closeMenu = (e) => {
        if (!menu.contains(e.target)) {
            menu.remove();
            document.removeEventListener('click', closeMenu);
        }
    };

    // Add slight delay to prevent immediate closure
    setTimeout(() => {
        document.addEventListener('click', closeMenu);
    }, 100);
}

// Removed duplicate deleteElement function - using the one with parameters below

function updateStats() {
    // Calculate show parking capacity (for show participants)
    const showParkingCapacity = layoutElements
        .filter(el => el.element_type === 'show_parking' || el.element_type === 'parking_section')
        .reduce((total, el) => total + (el.capacity || 50), 0); // Default 50 cars per section

    // Calculate visitor parking capacity
    const visitorParkingCapacity = layoutElements
        .filter(el => el.element_type === 'visitor_parking')
        .reduce((total, el) => total + (el.capacity || 30), 0); // Default 30 cars per section

    // Calculate vendor booth count
    const vendorBoothCount = layoutElements
        .filter(el => el.element_type === 'vendor_area' || el.element_type === 'single_booth')
        .reduce((total, el) => total + (el.capacity || 10), 0); // Default 10 booths per area

    // Total parking capacity
    const totalParkingCapacity = showParkingCapacity + visitorParkingCapacity;

    const stats = {
        showParking: showParkingCapacity,
        visitorParking: visitorParkingCapacity,
        vendorBooths: vendorBoothCount,
        totalCapacity: totalParkingCapacity
    };

    document.getElementById('stats-show-parking').textContent = stats.showParking;
    document.getElementById('stats-visitor-parking').textContent = stats.visitorParking;
    document.getElementById('stats-vendor-booths').textContent = stats.vendorBooths;
    document.getElementById('stats-total-capacity').textContent = stats.totalCapacity;
}

function updateElementList() {
    const tbody = document.getElementById('element-list');
    const countBadge = document.getElementById('element-count');
    const typeFilter = document.getElementById('type-filter');
    const showingSpan = document.getElementById('elements-showing');
    const totalSpan = document.getElementById('elements-total');

    // Combine grid elements and canvas drawings into one list
    const allElements = [];

    // Add grid elements
    layoutElements.forEach(element => {
        allElements.push({
            ...element,
            elementType: 'grid',
            displayType: element.element_type,
            displayName: element.name,
            position: { x: element.x_position, y: element.y_position },
            color: element.color
        });
    });

    // Add canvas drawings
    if (drawings && drawings.length > 0) {
        drawings.forEach(drawing => {
            // Calculate position from points
            let x = 0, y = 0;
            if (drawing.points && drawing.points.length > 0) {
                x = Math.min(...drawing.points.map(p => p.x));
                y = Math.min(...drawing.points.map(p => p.y));
            }

            allElements.push({
                ...drawing,
                elementType: 'canvas',
                displayType: drawing.tool,
                displayName: drawing.text || drawing.tool || 'Canvas Element',
                position: { x: x, y: y },
                color: drawing.style?.color || '#000000'
            });
        });
    }

    // Update total count badge
    countBadge.textContent = allElements.length;
    totalSpan.textContent = allElements.length;

    if (allElements.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-muted text-center py-3">
                    <i class="fas fa-info-circle"></i> Elements you add to the canvas will appear here
                </td>
            </tr>
        `;
        showingSpan.textContent = '0';
        updatePagination(0, 0);
        return;
    }

    // Apply filter
    const filterType = typeFilter ? typeFilter.value : '';
    const filteredElements = filterType ?
        allElements.filter(el => el.displayType === filterType) :
        allElements;

    if (filteredElements.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-muted text-center py-3">
                    <i class="fas fa-filter"></i> No elements match the current filter
                </td>
            </tr>
        `;
        showingSpan.textContent = '0';
        updatePagination(0, 0);
        return;
    }

    // Apply pagination
    const totalPages = Math.ceil(filteredElements.length / elementsPerPage);
    const startIndex = (currentPage - 1) * elementsPerPage;
    const endIndex = Math.min(startIndex + elementsPerPage, filteredElements.length);
    const pageElements = filteredElements.slice(startIndex, endIndex);

    // Update showing count
    showingSpan.textContent = `${startIndex + 1}-${endIndex}`;

    // Generate table rows
    const html = pageElements.map(element => {
        const isSelected = (element.elementType === 'grid' && selectedElement && selectedElement.id.toString() === element.id.toString()) ||
                          (element.elementType === 'canvas' && selectedDrawing && selectedDrawing.id === element.id);

        return `
            <tr class="${isSelected ? 'selected' : ''}"
                onclick="selectElementFromList('${element.elementType}', '${element.id}')" data-element-id="${element.id}">
                <td>
                    <input type="checkbox" class="form-check-input element-checkbox"
                           value="${element.id}" data-type="${element.elementType}" onclick="event.stopPropagation()">
                </td>
                <td>
                    <div class="element-color-indicator" style="background-color: ${element.color};"></div>
                </td>
                <td class="fw-medium">${element.displayName}</td>
                <td>
                    <span class="badge ${element.elementType === 'grid' ? 'bg-primary' : 'bg-info'} text-white">
                        ${formatElementType(element.displayType)}
                    </span>
                </td>
                <td class="text-muted small">
                    ${Math.round(element.position.x)}, ${Math.round(element.position.y)}
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="editElementFromList('${element.elementType}', '${element.id}', event)"
                            title="Edit Properties">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteElementFromList('${element.elementType}', '${element.id}', event)"
                            title="Delete Element">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    }).join('');

    tbody.innerHTML = html;
    updatePagination(filteredElements.length, totalPages);

    // Update delete selected button state
    updateDeleteSelectedButton();
}

// Pagination functions
function updatePagination(totalElements, totalPages) {
    const pagination = document.getElementById('element-pagination');

    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }

    let html = '';

    // Previous button
    html += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1}); return false;">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
    `;

    // Page numbers
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    if (startPage > 1) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(1); return false;">1</a></li>`;
        if (startPage > 2) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        html += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i}); return false;">${i}</a>
            </li>
        `;
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${totalPages}); return false;">${totalPages}</a></li>`;
    }

    // Next button
    html += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1}); return false;">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    `;

    pagination.innerHTML = html;
}

function changePage(page) {
    currentPage = page;
    updateElementList();
}

function filterByType() {
    currentPage = 1; // Reset to first page when filtering
    updateElementList();
}

// Element list interaction functions
function selectElementFromList(elementType, elementId) {
    if (elementType === 'grid') {
        const element = layoutElements.find(el => el.id.toString() === elementId.toString());
        if (element) {
            selectElement(element, false);
        }
    } else if (elementType === 'canvas') {
        const drawing = drawings.find(d => d.id === elementId);
        if (drawing) {
            clearSelection();
            removeAllResizeHandles();
            selectedDrawing = drawing;
            addDrawingHandles(drawing);
            updateSelectionDisplay();
        }
    }
}

function editElementFromList(elementType, elementId, event) {
    event.stopPropagation();
    if (elementType === 'grid') {
        const element = layoutElements.find(el => el.id.toString() === elementId.toString());
        if (element) {
            selectElement(element, true); // Select and show properties
        }
    } else if (elementType === 'canvas') {
        const drawing = drawings.find(d => d.id === elementId);
        if (drawing) {
            clearSelection();
            removeAllResizeHandles();
            selectedDrawing = drawing;
            addDrawingHandles(drawing);
            if (drawing.tool === 'road') {
                showRoadEditor(drawing);
            } else {
                showGeneralDrawingEditor(drawing);
            }
            updateSelectionDisplay();
        }
    }
}

function deleteElementFromList(elementType, elementId, event) {
    event.stopPropagation();
    if (elementType === 'grid') {
        deleteElement(elementId, event);
    } else if (elementType === 'canvas') {
        const drawing = drawings.find(d => d.id === elementId);
        if (drawing && confirm(`Delete ${drawing.tool}?`)) {
            const index = drawings.findIndex(d => d.id === elementId);
            drawings.splice(index, 1);
            redrawCanvas();
            updateElementList();
            saveToHistory(`Delete ${drawing.tool}`);
        }
    }
}

function formatElementType(type) {
    const typeMap = {
        // Grid elements
        'show_parking': 'Show Parking',
        'visitor_parking': 'Visitor Parking',
        'vendor_area': 'Vendor Area',
        'vendor_booth': 'Vendor Booth',
        'building': 'Building',
        'entrance': 'Entrance',
        'exit': 'Exit',
        'restroom': 'Restroom',
        'food_area': 'Food Area',
        'stage': 'Stage',
        'judging_area': 'Judging Area',
        'display_area': 'Display Area',
        'kids_area': 'Kids Area',
        'registration_area': 'Registration',
        'emergency': 'Emergency',
        // Canvas elements
        'road': 'Road',
        'rectangle': 'Rectangle',
        'circle': 'Circle',
        'island': 'Island',
        'text': 'Text'
    };
    return typeMap[type] || type.replace('_', ' ');
}

function selectElementById(elementId) {
    // Convert to string for comparison since HTML attributes are strings
    const element = layoutElements.find(el => el.id.toString() === elementId.toString());
    if (element) {
        selectElement(element, false); // Don't show properties when selecting from table
        updateElementList(); // Refresh to show selection
    } else {

    }
}

function showElementPropertiesById(elementId, event) {
    event.stopPropagation();
    const element = layoutElements.find(el => el.id.toString() === elementId.toString());
    if (element) {
        selectElement(element, true); // Select element AND show properties
        updateElementList(); // Refresh to show selection
    } else {

    }
}

function deleteElement(elementId, event) {
    event.stopPropagation();

    if (confirm('Are you sure you want to delete this element?')) {
        // Find element for history
        const element = layoutElements.find(el => el.id.toString() === elementId.toString());
        const elementName = element ? element.name : 'Element';

        // Remove from layoutElements array
        const index = layoutElements.findIndex(el => el.id.toString() === elementId.toString());
        if (index !== -1) {
            layoutElements.splice(index, 1);
        }

        // Clear selection if this element was selected
        if (selectedElement && selectedElement.id.toString() === elementId.toString()) {
            selectedElement = null;
        }

        saveToHistory(`Delete ${elementName}`);

        // Update displays
        updateElementList();
        updateStats();
        renderElements();
    }
}

function updateDeleteSelectedButton() {
    const checkboxes = document.querySelectorAll('.element-checkbox:checked');
    const deleteBtn = document.getElementById('delete-selected');

    if (deleteBtn) {
        deleteBtn.disabled = checkboxes.length === 0;
    }
}

function deleteSelectedElements() {
    const checkboxes = document.querySelectorAll('.element-checkbox:checked');
    const selectedIds = Array.from(checkboxes).map(cb => cb.value);

    if (selectedIds.length === 0) return;

    if (confirm(`Are you sure you want to delete ${selectedIds.length} selected element(s)?`)) {
        // Remove selected elements
        selectedIds.forEach(id => {
            const index = layoutElements.findIndex(el => el.id.toString() === id.toString());
            if (index !== -1) {
                layoutElements.splice(index, 1);
            }
        });

        // Clear selection if any selected element was deleted
        if (selectedElement && selectedIds.includes(selectedElement.id.toString())) {
            selectedElement = null;
        }

        saveToHistory(`Delete ${selectedIds.length} elements`);

        // Update displays
        updateElementList();
        updateStats();
        renderElements();
    }
}

function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('select-all');
    const elementCheckboxes = document.querySelectorAll('.element-checkbox');

    elementCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateDeleteSelectedButton();
}



function addResizeHandles(elementDiv, element) {
    // Ensure we remove any existing handles from this element first
    elementDiv.querySelectorAll('.resize-handle').forEach(handle => handle.remove());

    // Add 8 resize handles (corners and sides)
    const handles = ['nw', 'n', 'ne', 'e', 'se', 's', 'sw', 'w'];

    handles.forEach(direction => {
        const handle = document.createElement('div');
        handle.className = `resize-handle ${direction}`;
        handle.addEventListener('mousedown', function(e) {
            e.stopPropagation();
            startResize(e, element, direction);
        });
        elementDiv.appendChild(handle);
    });

    // Add rotation handle
    const rotationHandle = document.createElement('div');
    rotationHandle.className = 'rotation-handle';
    rotationHandle.addEventListener('mousedown', function(e) {
        e.stopPropagation();
        startElementRotation(e, element);
    });
    elementDiv.appendChild(rotationHandle);
}

// Grid element rotation functions
let isRotatingElement = false;
// Increase perceived rotation responsiveness for grid elements (DOM)
const GRID_ROTATION_SPEED = 1.0;
let rotatingElement = null;
let elementRotationStartAngle = 0;

function startElementRotation(e, element) {
    e.preventDefault();
    e.stopPropagation();

    isRotatingElement = true;
    rotatingElement = element;

    // Initialize rotation if not set
    if (typeof element.rotation === 'undefined') {
        element.rotation = 0;
    }

    const elementDiv = document.querySelector(`[data-element-id="${element.id}"]`);
    if (!elementDiv) {

        return;
    }

    // Compute center from model coords and zoom to follow circular mouse movement reliably
    const designCanvas = document.getElementById('design-canvas');
    const canvasRect = designCanvas.getBoundingClientRect();
    const centerX = canvasRect.left + (element.x_position + element.width / 2) * zoomLevel;
    const centerY = canvasRect.top + (element.y_position + element.height / 2) * zoomLevel;

    elementRotationStartAngle = Math.atan2(e.clientY - centerY, e.clientX - centerX);

    // Disable transitions while rotating for immediate response
    elementDiv.classList.add('rotating');



    document.addEventListener('mousemove', rotateElement);
    document.addEventListener('mouseup', stopElementRotation);
}

function rotateElement(e) {
    if (!isRotatingElement || !rotatingElement) return;

    const elementDiv = document.querySelector(`[data-element-id="${rotatingElement.id}"]`);
    if (!elementDiv) return;

    // Compute center from model coords and zoom to match canvas behavior
    const designCanvas = document.getElementById('design-canvas');
    const canvasRect = designCanvas.getBoundingClientRect();
    const centerX = canvasRect.left + (rotatingElement.x_position + rotatingElement.width / 2) * zoomLevel;
    const centerY = canvasRect.top + (rotatingElement.y_position + rotatingElement.height / 2) * zoomLevel;

    const currentAngle = Math.atan2(e.clientY - centerY, e.clientX - centerX);
    let deltaAngle = currentAngle - elementRotationStartAngle;

    // Normalize to avoid jumps when crossing -π/π (like canvas)
    if (deltaAngle > Math.PI) deltaAngle -= 2 * Math.PI;
    if (deltaAngle < -Math.PI) deltaAngle += 2 * Math.PI;

    // Convert to degrees
    const deltaAngleDegrees = deltaAngle * (180 / Math.PI);

    // Update element rotation (ensure it's a number)
    const currentRotation = parseFloat(rotatingElement.rotation) || 0;
    const newRotation = (currentRotation + deltaAngleDegrees * GRID_ROTATION_SPEED) % 360;

    rotatingElement.rotation = newRotation;

    // Apply rotation to the element with transform-origin center
    elementDiv.style.transformOrigin = 'center center';

    // Try different approaches to force the rotation to be visible
    elementDiv.style.transform = `rotate(${newRotation}deg)`;
    elementDiv.style.setProperty('transform', `rotate(${newRotation}deg)`, 'important');

    // Force immediate style recalculation
    elementDiv.classList.add('rotating');
    elementDiv.style.cssText += `; transform: rotate(${newRotation}deg) !important; transform-origin: center center !important;`;

    // The rotation is being applied but not visible - force a complete re-render
    // This is likely because the element is being re-rendered by the layout system

    // Force the entire canvas to re-render which will pick up the rotation
    renderElements();

    // Also force a style recalculation
    elementDiv.offsetHeight;

    // Try forcing a layout recalculation on the entire canvas
    const canvas = document.getElementById('design-canvas');
    if (canvas) {
        canvas.style.transform = canvas.style.transform; // Force style recalc
        canvas.offsetHeight;
    }



    // FORCE TEST - Try multiple ways to apply rotation
    elementDiv.style.cssText += `transform: rotate(${newRotation}deg) !important;`;
    elementDiv.setAttribute('style', elementDiv.getAttribute('style') + `transform: rotate(${newRotation}deg) !important;`);



    elementRotationStartAngle = currentAngle;
}

function stopElementRotation() {
    // Remove rotating class to restore normal transitions
    if (rotatingElement) {
        const elementDiv = document.querySelector(`[data-element-id="${rotatingElement.id}"]`);
        if (elementDiv) elementDiv.classList.remove('rotating');
    }
    isRotatingElement = false;
    rotatingElement = null;
    document.removeEventListener('mousemove', rotateElement);
    document.removeEventListener('mouseup', stopElementRotation);
}

let isResizing = false;
let resizeDirection = '';
let resizeStartPos = { x: 0, y: 0 };
let resizeStartSize = { width: 0, height: 0 };
let resizeStartPosition = { x: 0, y: 0 };

function startResize(e, element, direction) {
    isResizing = true;
    resizeDirection = direction;

    resizeStartPos.x = e.clientX;
    resizeStartPos.y = e.clientY;
    resizeStartSize.width = element.width;
    resizeStartSize.height = element.height;
    resizeStartPosition.x = element.x_position;
    resizeStartPosition.y = element.y_position;

    document.addEventListener('mousemove', resize);
    document.addEventListener('mouseup', stopResize);

    e.preventDefault();
}

function resize(e) {
    if (!isResizing || !selectedElement) return;

    const deltaX = e.clientX - resizeStartPos.x;
    const deltaY = e.clientY - resizeStartPos.y;

    let newWidth = resizeStartSize.width;
    let newHeight = resizeStartSize.height;
    let newX = resizeStartPosition.x;
    let newY = resizeStartPosition.y;

    // Apply resize based on direction
    if (resizeDirection.includes('e')) {
        newWidth = Math.max(50, resizeStartSize.width + deltaX / zoomLevel);
    }
    if (resizeDirection.includes('w')) {
        newWidth = Math.max(50, resizeStartSize.width - deltaX / zoomLevel);
        newX = resizeStartPosition.x + (resizeStartSize.width - newWidth);
    }
    if (resizeDirection.includes('s')) {
        newHeight = Math.max(30, resizeStartSize.height + deltaY / zoomLevel);
    }
    if (resizeDirection.includes('n')) {
        newHeight = Math.max(30, resizeStartSize.height - deltaY / zoomLevel);
        newY = resizeStartPosition.y + (resizeStartSize.height - newHeight);
    }

    // Update element
    selectedElement.width = newWidth;
    selectedElement.height = newHeight;
    selectedElement.x_position = Math.max(0, newX);
    selectedElement.y_position = Math.max(0, newY);

    renderElements();
    updateElementList();

    // Show size indicator during grid element resize
    const bounds = {
        x: selectedElement.x_position,
        y: selectedElement.y_position,
        width: selectedElement.width,
        height: selectedElement.height,
        centerX: selectedElement.x_position + selectedElement.width / 2,
        centerY: selectedElement.y_position + selectedElement.height / 2
    };
    showSizeIndicator(selectedElement, bounds, selectedElement.element_type);
}

function stopResize() {
    if (isResizing && selectedElement) {
        saveToHistory(`Resize ${selectedElement.name}`);

        // Update group data if this element was selected from group manager
        if (selectedFromGroupManager) {
            const element = selectedElement;
            selectedFromGroupManager.originalData.width = element.width;
            selectedFromGroupManager.originalData.height = element.height;
            selectedFromGroupManager.originalData.x_position = element.x_position;
            selectedFromGroupManager.originalData.y_position = element.y_position;
        }
    }
    isResizing = false;
    resizeDirection = '';
    document.removeEventListener('mousemove', resize);
    document.removeEventListener('mouseup', stopResize);
}

function saveLayout() {
    if (!layoutId) {
        alert('No layout ID found. Please refresh the page and try again.');
        return;
    }



    const data = {
        layout_id: layoutId,
        elements: layoutElements,
        drawings: drawings,
        groups: elementGroups
    };

    fetch('<?= URLROOT ?>/layout_manager/saveLayout', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Layout saved successfully!');
        } else {
            alert('Error saving layout: ' + data.error);
        }
    })
    .catch(error => {

        alert('Failed to save layout');
    });
}

function loadTemplate() {
    const modalElement = document.getElementById('loadTemplateModal');

    // SOLUTION: Move modal outside stacking context by appending to body
    if (modalElement.parentNode !== document.body) {
        document.body.appendChild(modalElement);
    }

    // Reset any existing styles
    modalElement.style.zIndex = '';
    modalElement.style.position = '';

    const modal = new bootstrap.Modal(modalElement);
    modal.show();
}

function applyTemplate() {
    const templateId = document.getElementById('templateSelect').value;
    if (!templateId) {
        alert('Please select a template');
        return;
    }

    console.log('Loading template:', templateId, 'for show:', showId);

    if (confirm('This will create a new layout from the selected template. Continue?')) {
        fetch('<?= URLROOT ?>/layout_manager/createFromTemplate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                show_id: showId,
                template_id: templateId
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('Template response:', data);
            if (data.success) {
                // Redirect to the newly created layout
                const newUrl = '<?= URLROOT ?>/layout_manager/designer/' + showId + '/' + data.layout_id;
                console.log('Redirecting to:', newUrl);
                window.location.href = newUrl;
            } else {
                alert('Error loading template: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to load template');
        });
    }
}

function saveAsTemplate() {
    const name = prompt('Enter template name:');
    if (!name || name.trim() === '') {
        return;
    }

    const description = prompt('Enter template description (optional):') || '';
    const isPublic = confirm('Make this template public for other users?');

    // Get canvas dimensions from the drawing canvas element
    const drawingCanvas = document.getElementById('drawing-canvas');
    const canvasWidth = drawingCanvas ? drawingCanvas.width : 2000;
    const canvasHeight = drawingCanvas ? drawingCanvas.height : 1400;



    const data = {
        name: name.trim(),
        description: description.trim(),
        is_public: isPublic ? 1 : 0,
        show_id: showId,
        layout_id: layoutId,
        elements: layoutElements,
        drawings: drawings,
        groups: elementGroups,
        canvas_width: canvasWidth,
        canvas_height: canvasHeight
    };

    fetch('<?= URLROOT ?>/layout_manager/saveTemplate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Template saved successfully!');
        } else {
            alert('Error saving template: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to save template');
    });
}

// Overlapping elements function temporarily removed to restore save functionality

function clearCanvas() {
    if (confirm('Are you sure you want to clear all elements?')) {
        const elementCount = layoutElements.length;
        layoutElements = [];
        selectElement(null);
        saveToHistory(`Clear canvas (${elementCount} elements)`);
        renderElements();
        updateStats();
        updateElementList();
    }
}

function zoomIn() {
    zoomLevel = Math.min(zoomLevel * 1.2, 3);
    updateZoom();
}

function zoomOut() {
    zoomLevel = Math.max(zoomLevel / 1.2, 0.5);
    updateZoom();
}

function resetZoom() {
    zoomLevel = 1;
    updateZoom();
}

function updateZoom() {
    const canvas = document.getElementById('design-canvas');
    const drawingCanvas = document.getElementById('drawing-canvas');
    const container = canvas.parentElement;

    canvas.style.transform = `scale(${zoomLevel})`;
    canvas.style.transformOrigin = 'top left';

    // Also scale the drawing canvas
    drawingCanvas.style.transform = `scale(${zoomLevel})`;
    drawingCanvas.style.transformOrigin = 'top left';

    // Scale the container grid to match the canvas grid
    const scaledGridSize = 10 * zoomLevel;
    container.style.backgroundSize = `${scaledGridSize}px ${scaledGridSize}px`;

    document.getElementById('zoom-level').textContent = Math.round(zoomLevel * 100) + '%';
}

// Undo/Redo System
function saveToHistory(action = 'unknown') {
    // Create a deep copy of the current state
    const state = {
        elements: JSON.parse(JSON.stringify(layoutElements)),
        drawings: JSON.parse(JSON.stringify(drawings)), // Include drawings
        groups: JSON.parse(JSON.stringify(elementGroups)), // Include groups
        selectedElementId: selectedElement ? selectedElement.id : null,
        selectedRoadId: selectedRoad ? selectedRoad.id : null, // Include selected road
        action: action,
        timestamp: Date.now()
    };

    // Remove any history after current index (when user made changes after undo)
    history = history.slice(0, historyIndex + 1);

    // Add new state
    history.push(state);

    // Limit history size
    if (history.length > MAX_HISTORY) {
        history.shift();
    } else {
        historyIndex++;
    }

    updateUndoRedoButtons();

}

function undo() {
    if (historyIndex > 0) {
        historyIndex--;
        const state = history[historyIndex];

        // Restore state
        layoutElements = JSON.parse(JSON.stringify(state.elements));

        // Restore drawings
        if (state.drawings) {
            drawings = JSON.parse(JSON.stringify(state.drawings));
            redrawCanvas();
        }

        // Restore groups
        if (state.groups) {
            elementGroups = JSON.parse(JSON.stringify(state.groups));
            updateGroupManager();
        }

        // Restore selection
        if (state.selectedElementId) {
            selectedElement = layoutElements.find(el => el.id.toString() === state.selectedElementId.toString());
        } else {
            selectedElement = null;
        }

        // Restore road selection
        if (state.selectedRoadId) {
            selectedRoad = drawings.find(d => d.id === state.selectedRoadId);
            if (selectedRoad) {
                showRoadEditor(selectedRoad);
            }
        } else {
            selectedRoad = null;
            hideRoadEditor();
        }

        // Update displays
        renderElements();
        updateElementList();
        updateStats();

        updateUndoRedoButtons();

    }
}

function redo() {
    if (historyIndex < history.length - 1) {
        historyIndex++;
        const state = history[historyIndex];

        // Restore state
        layoutElements = JSON.parse(JSON.stringify(state.elements));

        // Restore drawings
        if (state.drawings) {
            drawings = JSON.parse(JSON.stringify(state.drawings));
            redrawCanvas();
        }

        // Restore groups
        if (state.groups) {
            elementGroups = JSON.parse(JSON.stringify(state.groups));
            updateGroupManager();
        }

        // Restore selection
        if (state.selectedElementId) {
            selectedElement = layoutElements.find(el => el.id.toString() === state.selectedElementId.toString());
        } else {
            selectedElement = null;
        }

        // Restore road selection
        if (state.selectedRoadId) {
            selectedRoad = drawings.find(d => d.id === state.selectedRoadId);
            if (selectedRoad) {
                showRoadEditor(selectedRoad);
            }
        } else {
            selectedRoad = null;
            hideRoadEditor();
        }

        // Update displays
        renderElements();
        updateElementList();
        updateStats();

        updateUndoRedoButtons();

    }
}

function updateUndoRedoButtons() {
    const undoBtn = document.getElementById('undo-btn');
    const redoBtn = document.getElementById('redo-btn');

    if (undoBtn) {
        undoBtn.disabled = historyIndex <= 0;
        undoBtn.title = historyIndex > 0 ? `Undo: ${history[historyIndex].action} (Ctrl+Z)` : 'Undo (Ctrl+Z)';
    }

    if (redoBtn) {
        redoBtn.disabled = historyIndex >= history.length - 1;
        redoBtn.title = historyIndex < history.length - 1 ? `Redo: ${history[historyIndex + 1].action} (Ctrl+Y)` : 'Redo (Ctrl+Y)';
    }
}

// Drag functionality
function startDrag(e, element) {


    // Don't auto-select here - let the click event handle selection
    // This prevents double selection calls

    // Initialize drag state and intent tracking
    isDragging = true;
    movedDuringDrag = false;
    dragStartMouse.x = e.clientX;
    dragStartMouse.y = e.clientY;
    dragStartElementPos.x = element.x_position;
    dragStartElementPos.y = element.y_position;

    // Check if we have a mixed selection (grid + canvas)
    const hasMixedSelection = selectedElements.length > 0 && selectedDrawings.length > 0;
    const hasMultipleGridElements = selectedElements.length > 1;
    const hasMultipleCanvasDrawings = selectedDrawings.length > 1;

    // Store initial positions for group dragging (grid elements)
    // Include grid elements if: multiple grid elements OR mixed selection
    if (hasMultipleGridElements || hasMixedSelection) {
        groupDragStartPositions = selectedElements.map(el => ({
            element: el,
            startX: el.x_position,
            startY: el.y_position
        }));
        groupDragStartPositions.forEach((item, index) => {
            // Add dragging class to disable transitions
            const elementDiv = document.getElementById('element_' + item.element.id);
            if (elementDiv) elementDiv.classList.add('dragging');
        });
    } else {
        groupDragStartPositions = [];
    }

    // Store initial positions for group dragging (canvas drawings)
    // Include canvas drawings if: multiple canvas drawings OR mixed selection
    if (hasMultipleCanvasDrawings || hasMixedSelection) {
        groupDragStartDrawings = selectedDrawings.map(drawing => ({
            drawing: drawing,
            startPoints: drawing.points.map(point => ({ x: point.x, y: point.y }))
        }));

    } else {
        groupDragStartDrawings = [];
    }

    const rect = e.target.getBoundingClientRect();
    dragOffset.x = e.clientX - rect.left;
    dragOffset.y = e.clientY - rect.top;

    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', stopDrag);

    e.preventDefault();
}

function drag(e) {
    if (!isDragging || isResizing) return;

    // Apply a small pixel threshold before moving to avoid accidental drags
    const dx = e.clientX - dragStartMouse.x;
    const dy = e.clientY - dragStartMouse.y;
    if (Math.abs(dx) < DRAG_PIXEL_THRESHOLD && Math.abs(dy) < DRAG_PIXEL_THRESHOLD) {
        return; // not a real move yet
    }

    movedDuringDrag = true;

    const canvas = document.getElementById('design-canvas');
    const canvasRect = canvas.getBoundingClientRect();

    // Check if we're dragging a group (multiple selected elements or drawings OR mixed selection)
    const hasMixedSelection = selectedElements.length > 0 && selectedDrawings.length > 0;
    const hasMultipleGridElements = selectedElements.length > 1;
    const hasMultipleCanvasDrawings = selectedDrawings.length > 1;
    const isGroupDrag = hasMixedSelection || hasMultipleGridElements || hasMultipleCanvasDrawings;

    if (isGroupDrag) {
        // Group drag - use mouse delta directly, not element position calculation
        const mouseDeltaX = (e.clientX - dragStartMouse.x) / zoomLevel;
        const mouseDeltaY = (e.clientY - dragStartMouse.y) / zoomLevel;

        // Apply mouse delta to grid elements
        groupDragStartPositions.forEach((item, index) => {
            const newPosX = item.startX + mouseDeltaX;
            const newPosY = item.startY + mouseDeltaY;

            // Apply boundary checking
            const boundedX = Math.max(0, Math.min(newPosX, 2000 - item.element.width));
            const boundedY = Math.max(0, Math.min(newPosY, 1400 - item.element.height));

            item.element.x_position = boundedX;
            item.element.y_position = boundedY;
        });

        // Apply mouse delta to canvas drawings
        groupDragStartDrawings.forEach((item, index) => {
            item.drawing.points.forEach((point, pointIndex) => {
                point.x = item.startPoints[pointIndex].x + mouseDeltaX;
                point.y = item.startPoints[pointIndex].y + mouseDeltaY;
            });
        });
    } else if (selectedElement) {
        // Single element drag - use the original calculation with dragOffset
        const newX = (e.clientX - canvasRect.left - dragOffset.x) / zoomLevel;
        const newY = (e.clientY - canvasRect.top - dragOffset.y) / zoomLevel;
        // Single element drag
        selectedElement.x_position = Math.max(0, Math.min(newX, 2000 - selectedElement.width));
        selectedElement.y_position = Math.max(0, Math.min(newY, 1400 - selectedElement.height));
    }



    renderElements();
    redrawCanvas(); // Update canvas drawings too
    updateElementList(); // Update the table to show new position
}

function stopDrag() {
    if (isDragging && movedDuringDrag) {
        if (selectedElements.length > 1 || selectedDrawings.length > 1) {
            // Group move (grid elements and/or canvas drawings)
            const gridNames = selectedElements.map(el => el.name).join(', ');
            const drawingNames = selectedDrawings.map(d => d.tool).join(', ');
            const allNames = [gridNames, drawingNames].filter(n => n).join(', ');
            const totalCount = selectedElements.length + selectedDrawings.length;
            saveToHistory(`Move group (${totalCount} elements): ${allNames.substring(0, 50)}${allNames.length > 50 ? '...' : ''}`);
        } else if (selectedElement) {
            // Single element move
            saveToHistory(`Move ${selectedElement.name}`);

            // Update group data if this element was selected from group manager
            if (selectedFromGroupManager) {
                selectedFromGroupManager.originalData.x_position = selectedElement.x_position;
                selectedFromGroupManager.originalData.y_position = selectedElement.y_position;
            }
        }
    }

    // Clean up drag state
    isDragging = false;
    movedDuringDrag = false;

    // Clear canvas drag flag with a small delay to prevent immediate click interference
    setTimeout(() => {
        isDraggingCanvasDrawing = false;
    }, 50);

    // Remove dragging class from all elements
    if (groupDragStartPositions.length > 0) {
        groupDragStartPositions.forEach(item => {
            const elementDiv = document.getElementById('element_' + item.element.id);
            if (elementDiv) elementDiv.classList.remove('dragging');
        });
    }

    groupDragStartPositions = []; // Clear group drag positions
    groupDragStartDrawings = []; // Clear canvas group drag positions

    // Update visual styling to restore selection borders
    updateSelectionDisplay();

    document.removeEventListener('mousemove', drag);
    document.removeEventListener('mouseup', stopDrag);
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Handle Ctrl+Z and Ctrl+Y even in input fields
    if (e.ctrlKey || e.metaKey) {
        switch(e.key.toLowerCase()) {
            case 'z':
                if (e.shiftKey) {
                    redo(); // Ctrl+Shift+Z = Redo
                } else {
                    undo(); // Ctrl+Z = Undo
                }
                e.preventDefault();
                return;
            case 'y':
                redo(); // Ctrl+Y = Redo
                e.preventDefault();
                return;
            case 'a':
                // Ctrl+A = Select All
                selectAllElements();
                e.preventDefault();
                return;
        }
    }

    // Handle other keys
    switch(e.key) {
        case 'Escape':
            // Escape = Clear selection
            clearSelection();
            removeAllResizeHandles();
            document.getElementById('element-properties').style.display = 'none';
            e.preventDefault();
            break;
        case 'Delete':
            // Delete = Delete selected elements
            deleteSelectedElements();
            e.preventDefault();
            break;
    }

    // Only handle other shortcuts when not typing in input fields
    if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;

    switch(e.key) {
        case 'Enter':
        case 'F2':
            // Open properties for selected element
            if (selectedElement) {
                selectElement(selectedElement, true);
                e.preventDefault();
            }
            break;
        case 'Delete':
            // Delete selected element
            if (selectedElement) {
                deleteElement(selectedElement.id, e);
                e.preventDefault();
            }
            break;
        case 'Escape':
            // Deselect element and hide properties
            selectElement(null);
            e.preventDefault();
            break;
    }
});
</script>

<!-- DOM portal handles modal positioning, no CSS overrides needed -->

<?php require APPROOT . '/views/includes/footer.php'; ?>