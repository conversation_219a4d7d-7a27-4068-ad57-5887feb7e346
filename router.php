<?php
/**
 * Router for PHP Development Server
 * 
 * This file handles URL routing for the PHP development server
 * to simulate Apache's mod_rewrite functionality.
 */

$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$uri = urldecode($uri);

// Handle static files (CSS, JS, images, etc.)
if (preg_match('/\.(css|js|jpg|jpeg|png|gif|ico|svg|webp|woff|woff2|ttf|eot)$/', $uri)) {
    // Check if file exists in public directory first
    $publicFile = __DIR__ . '/public' . $uri;
    if (file_exists($publicFile)) {
        return false; // Let PHP serve the file
    }
    
    // Check if file exists in root directory
    $rootFile = __DIR__ . $uri;
    if (file_exists($rootFile)) {
        return false; // Let PHP serve the file
    }
    
    // File not found
    http_response_code(404);
    echo "File not found: " . $uri;
    return true;
}

// Handle uploads directory
if (strpos($uri, '/uploads/') === 0) {
    $uploadFile = __DIR__ . $uri;
    if (file_exists($uploadFile)) {
        return false; // Let PHP serve the file
    }
    
    http_response_code(404);
    echo "Upload file not found: " . $uri;
    return true;
}

// Handle public directory
if (strpos($uri, '/public/') === 0) {
    $publicFile = __DIR__ . $uri;
    if (file_exists($publicFile)) {
        return false; // Let PHP serve the file
    }
    
    http_response_code(404);
    echo "Public file not found: " . $uri;
    return true;
}

// Handle API routes
if (strpos($uri, '/api/') === 0) {
    // Set the URL parameter for the application
    $_GET['url'] = ltrim($uri, '/');
    require_once __DIR__ . '/index.php';
    return true;
}

// Handle all other routes through the main application
if ($uri !== '/') {
    // Remove leading slash and set as URL parameter
    $_GET['url'] = ltrim($uri, '/');
}

// Include the main application entry point
require_once __DIR__ . '/index.php';
return true;
?>
