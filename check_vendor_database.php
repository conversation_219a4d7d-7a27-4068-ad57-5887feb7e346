<?php
/**
 * Check vendor database tables and structure
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>Vendor Database Structure Check</h1>";

try {
    $db = new Database();
    echo "✅ Database connection successful<br><br>";
    
    // Check required tables for vendor system
    $requiredTables = [
        'event_layouts' => [
            'id', 'show_id', 'name', 'width', 'height', 'background_color', 'created_at'
        ],
        'layout_elements' => [
            'id', 'layout_id', 'element_type', 'name', 'x_position', 'y_position', 
            'width', 'height', 'z_index', 'is_visible', 'properties'
        ],
        'vendor_booth_assignments' => [
            'id', 'layout_element_id', 'show_id', 'assignee_type', 'assignee_id', 
            'assignee_name', 'booth_number', 'booth_size', 'power_required', 
            'table_required', 'chair_count', 'assignment_fee', 'status', 'created_at'
        ]
    ];
    
    foreach ($requiredTables as $tableName => $expectedColumns) {
        echo "<h2>Table: {$tableName}</h2>";
        
        // Check if table exists
        $db->query("SHOW TABLES LIKE '{$tableName}'");
        $tableExists = $db->single();
        
        if ($tableExists) {
            echo "✅ Table exists<br>";
            
            // Check table structure
            $db->query("DESCRIBE {$tableName}");
            $columns = $db->resultSet();
            
            echo "<strong>Current columns:</strong><br>";
            $existingColumns = [];
            foreach ($columns as $column) {
                $existingColumns[] = $column->Field;
                echo "  - {$column->Field} ({$column->Type}) " . 
                     ($column->Null === 'YES' ? 'NULL' : 'NOT NULL') . 
                     ($column->Key ? " KEY: {$column->Key}" : '') . "<br>";
            }
            
            // Check for missing columns
            $missingColumns = array_diff($expectedColumns, $existingColumns);
            if (!empty($missingColumns)) {
                echo "<strong>❌ Missing columns:</strong><br>";
                foreach ($missingColumns as $missing) {
                    echo "  - {$missing}<br>";
                }
            } else {
                echo "✅ All expected columns present<br>";
            }
            
            // Show sample data count
            $db->query("SELECT COUNT(*) as count FROM {$tableName}");
            $count = $db->single();
            echo "<strong>Record count:</strong> {$count->count}<br>";
            
        } else {
            echo "❌ Table does not exist<br>";
            echo "<strong>SQL to create table:</strong><br>";
            
            // Provide SQL to create missing tables
            switch ($tableName) {
                case 'event_layouts':
                    echo "<pre>CREATE TABLE event_layouts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    show_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    width INT DEFAULT 800,
    height INT DEFAULT 600,
    background_color VARCHAR(7) DEFAULT '#f8f9fa',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_show_id (show_id)
);</pre>";
                    break;
                    
                case 'layout_elements':
                    echo "<pre>CREATE TABLE layout_elements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    layout_id INT NOT NULL,
    element_type VARCHAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    x_position INT DEFAULT 0,
    y_position INT DEFAULT 0,
    width INT DEFAULT 100,
    height INT DEFAULT 100,
    z_index INT DEFAULT 1,
    is_visible TINYINT(1) DEFAULT 1,
    properties JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_layout_id (layout_id),
    INDEX idx_element_type (element_type)
);</pre>";
                    break;
                    
                case 'vendor_booth_assignments':
                    echo "<pre>CREATE TABLE vendor_booth_assignments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    layout_element_id INT NOT NULL,
    show_id INT NOT NULL,
    assignee_type VARCHAR(50) DEFAULT 'vendor',
    assignee_id INT NULL,
    assignee_name VARCHAR(255) NOT NULL,
    booth_number VARCHAR(50) NULL,
    booth_size VARCHAR(50) NULL,
    power_required TINYINT(1) DEFAULT 0,
    table_required TINYINT(1) DEFAULT 0,
    chair_count INT DEFAULT 0,
    assignment_fee DECIMAL(10,2) DEFAULT 0.00,
    status ENUM('assigned', 'confirmed', 'checked_in', 'cancelled') DEFAULT 'assigned',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_layout_element (layout_element_id),
    INDEX idx_show_id (show_id),
    INDEX idx_status (status)
);</pre>";
                    break;
            }
        }
        echo "<hr>";
    }
    
    // Check for shows with layouts
    echo "<h2>Shows with Event Layouts</h2>";
    $db->query("
        SELECT s.id, s.name, COUNT(el.id) as layout_count 
        FROM shows s 
        LEFT JOIN event_layouts el ON s.id = el.show_id 
        GROUP BY s.id, s.name 
        ORDER BY s.id DESC 
        LIMIT 10
    ");
    $showsWithLayouts = $db->resultSet();
    
    if ($showsWithLayouts) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Show ID</th><th>Show Name</th><th>Layout Count</th></tr>";
        foreach ($showsWithLayouts as $show) {
            echo "<tr><td>{$show->id}</td><td>{$show->name}</td><td>{$show->layout_count}</td></tr>";
        }
        echo "</table>";
    } else {
        echo "No shows found or shows table doesn't exist.<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

echo "<br><h2>Vendor System Status Summary</h2>";
echo "<p>Review the results above to identify missing database components for the vendor management system.</p>";
?>
