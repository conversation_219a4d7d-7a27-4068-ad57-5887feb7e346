-- Add missing columns to template_elements table
-- These columns are needed to properly save and restore element properties when using templates

ALTER TABLE `template_elements` 
ADD COLUMN `capacity` INT NULL AFTER `properties`,
ADD COLUMN `category_id` INT UNSIGNED NULL AFTER `capacity`,
ADD COLUMN `handicap_accessible` TINYINT(1) DEFAULT 0 AFTER `category_id`;

-- Note: We don't add foreign key for category_id because templates are generic
-- and categories are show-specific

